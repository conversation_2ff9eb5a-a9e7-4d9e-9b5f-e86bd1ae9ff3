module,Issue Discription,screenshot new society,Screenshot old society,Issue Type,status,Assigned To,Dev  Status,Screenshot before DEV fix ,Screenshot after DEV fix ,Testing Status,Remarks
DASHBOARD ,,,,,,,,,,,
DASHBOARD ,"balance due , allottees is mismatched in old and new society 
please check the screenshot ",https://prnt.sc/T1SL8pXpK-eT,https://prnt.sc/0mmUdPb_Y3tE,FUNCTIONAL,OPEN,Suraj Jamdade Shweta Danawale,Ready To Test,https://prnt.sc/H-az5VrgsoHV,https://prnt.sc/ZAvcptxyLLUb,VERIFIED,Only issue in maintenance dues balance 
"DASHBOARD >>view all ( Balance Dues, Allottees)","when user click on see all button link he shoud be redirected to their respective pages 
now it shows page 404 not found ; please check the screenshot ",https://prnt.sc/RTAz-dsK1Qjb,,FUNCTIONAL,OP<PERSON>,<PERSON><PERSON><PERSON>,Ready To Test,,,VERIFIED,
"DASHBOARD >>( Bank & Cash, Monthly Expenses)","no view all button is required on these sections 
remove view all button ",,,Ui,OPEN,Bilal Momin,Ready To Test,,,VERIFIED,
DASHBOARD >>( survey),"show add new button instead of view all button 
and when user click on add new button he should redirected to page >> New Communication
(Notice / Circular / MoM / Announcement / Survey)",,,"FUNCTIONAL, Ui",OPEN,Bilal Momin,Ready To Test,,,VERIFIED,
DASHBOARD >> Monthly Expenses,plese check the screenshot ,https://prnt.sc/XUoQm3HUO70l,,Ui,OPEN,Kumail Rizvi,Ready To Test,,,VERIFIED,
HELPDESK,,,,,,,,,,,
p,"- help note is missing - helpnote content point number is missing 
- export report ( print , pdf , excel )functionality is not working shows error message "" 404 page not found"" - print functionality is missing 

- post reply / post internal note / assign issue Conversation history does not refresh automatically; the user must refresh the page manually. DONE

- Replies posted by the user are automatically wrapped in <p> (paragraph) HTML tags. DONE

-  Incorrect Assignee Name:- When an issue is assigned to any staff member, the assignee name is incorrectly displayed as ""Atul Lohar"", regardless of the actual assignee.

- Status Toggle Behavior:-  When the user taps on the issue status: If the current status is ""On Hold"", it should change to ""Reopen"". If the current status is ""Reopen"", it should change back to ""On Hold"". DONE

- instead of print button show back button DONE

- Status Change Not Reflected in Conversation History:- When the issue status is changed (e.g., from ""On Hold"" to ""Reopen"" or vice versa), this change is not highlighted or recorded in tphe conversation history DONE

 - new job card funcationality is missing "" currently shows error 404 page not found "" :- if job  updated status  is attampted do not show the comment field.

not able to save the job card;  job deatils filed should required gto be autoreflected .

 - edit functionality  - not able to enter complaint details in complaint details text filed ; also many of the fields are not autopopulated on edit like ( assign to, help topic and complaint summary)  

- change status and multiple delete funcationality is not working 

filters in the Member Queries section are currently not functioning as expected.

2. Helpdesk>> member quries >> reports>> export reports
Print Functionality: Missing from the export report.

PDF Export: The following columns should be excluded from the PDF output: Id Soc, Id, Fk, HelpTopic, Id, and Body.

Excel Export: The report includes many unnecessary columns, and some required columns are displaying incorrect data.

3. UI Spacing:
The reply message section is touching the top edge of the container. Please add appropriate spacing or padding at the top to improve visual clarity.

4. Incorrect Admin Name Displayed:
The name of the admin is appearing unintentionally in the reply, which is incorrect and should be fixed.

“Updated By” Label Timing Issue:
The Updated By: [Admin Name] label takes time to reflect in the conversation. Please ensure this updates in real-time or as quickly as possible.

 5. Post reply Tab – Dropdown Field Behavior: In the Post tab, the ""To"" dropdown field should not be disabled. Users should be allowed to select the ""Do not reply mail"" option from the dropdown.

6. Duplicate Complaints on Multiple Attachments:
When multiple attachments are uploaded under the same ticket number, the system incorrectly creates multiple complaint entries.

Expected Behavior: It should create only one complaint for the ticket and display the attachments with a count (e.g., Attachment (3)).

7. Attachments Missing in Member Details Section:
Uploaded attachments are not visible or available for download in the Member Details section.

Expected Behavior: All uploaded attachments should be listed and downloadable from the Member Details view.

8. When a new job card is added (e.g., Job Card #19 assigned to Kumail), the system generates the message:
""New Job Card #19 created and assigned to Kumail.""

Problem: This message is not appearing in the conversation history, even though the job card is successfully created.

Expected Behavior: The notification message for newly created job cards should automatically appear in the conversation history for proper tracking and visibility.

When the user taps on the Print icon, the system currently downloads a PDF instead of opening the print dialog/page.

Incorrect Status Display After Creation: helptopic 

When a user creates a new help topic with: Due Hours: 1 Status: Active

After saving, the help topic incorrectly appears with Status: Inactive.

Expected Behavior: The saved help topic should retain the selected status (Active).

Inconsistent Validation Between Create and Edit:

While editing the same help topic, the system throws a validation error:
""The value entered is less than the minimum value of 4. Please check and try again.""

However, this validation is missing during creation, allowing users to save help topics with due hours below 4. Expected Behavior: The same validation rule (minimum due hours = 4) should be applied consistently during both creation and editing of a help topic. Incorrect Redirection on ""New Help Topic"" :-  When the user clicks on ""New Help Topic"" under Add Complaint, they are incorrectly redirected to the New Vendor popup. It should open the New Help Topic popup.

2. Label Issue: “Attachmentsis”:-  label “Attachmentsis” is incorrectly spelled. Rename it to “Attachment” and display a count next to it — e.g., attachment (1)  based on the number of uploaded files.

3. Attachment Download Not Working :- Download Attachment functionality is currently not working.

4. Incorrect Timestamps in Conversation History
Actions such as: Post Reply, Post Internal Note and Assign Issue are showing incorrect timestamps in the conversation history.
Timestamps should accurately reflect the time the action was performed.

5. Incorrect Name for Internal Notes:- When posting an Internal Note, the conversation history displays an incorrect name. The correct user name should be shown.

6. Ticket Reopen Logic Issue :-Users are currently able to reopen the same ticket multiple times, which should not be allowed. Once a ticket is reopened, the ""Reopen"" option should be disabled.
The ticket status should toggle appropriately (e.g., show as On Hold after being reopened, and vice versa).

7. HTML Tag Issue in Job Card Details:- In the Job Details section under Job Cards, the content is being rendered with raw <p> tags.
",,,FUNCTIONAL,OPEN,Kumail Rizvi Kumail Rizvi,Ready To Test,https://drive.google.com/file/d/11SwJR6SSL8Ym3CvaAXe47XFLnFVP3Pje/view?usp=drive_link,"https://drive.google.com/file/d/11SwJR6SSL8Ym3CvaAXe47XFLnFVP3Pje/view?usp=drive_link



https://ibb.co/Q3CKbgXV ATUL LOHAR & HTML tag issue

https://ibb.co/DDswX68N https://ibb.co/4gVd5rCk Toggle on hold issue, Back button & status change added in comment history
 ",VERIFIED,
HELPDESK >>New complaint ,"- please check the screenshot 
follow old society screenshot ",https://prnt.sc/OmyP1NhPzaC6,https://prnt.sc/5-zzRHGCGsL5,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,VERIFIED,
HELPDESK >>Help Topics >> action ," Delete Functionality Not Working Properly
When the user clicks the Delete button:
-Instead of actually deleting the help topic, the system just changes its status from Active to Inactive.
-This is incorrect — the help topic should be properly deleted, not just marked as inactive.
- delete confirmation message is missing ""Do you want to discard this issue?"" ""yes"" ""cancel""
- now when user try to delete the helptopic it shows error message "" network error """,https://prnt.sc/SLfnJEmmnOmr,,FUNCTIONAL,OPEN,Suraj Jamdade Kumail Rizvi,Ready To Test,,,VERIFIED,
HELPDESK >>Help Topics >> action >> edit,"1. Issue with Help Topic Status Display in Edit Mode
When a help topic is active and the user clicks the edit button:
-The system incorrectly shows the inactive radio button as selected.
- The user is unable to select the active option again.
- However, in the help topic list, it still shows the topic as active — which causes confusion and is the wrong behavior. 
- after saving the helptopic it shows the updated help topic on top which is incorrect after updation it required on same page DONE

2. Missing Validation on Mandatory Fields
Currently, the system only validates the Due Hours field.
-If a user enters a value in Due Hours and clicks Update, the help topic gets updated even if required fields like Parent Topic and Auto Assign To are left blank.
- This is incorrect — the system should not allow updates unless all required fields are filled in. DONE
- not able to make complaint private to public "" it remais as private "" which is incorrect . DONE

- do not show the validation errror message on ""auto assign to "" field if left blank 

3. Due Hours Value Not Retained on Re-Edit
After updating a help topic:
-If the user goes back to edit the same topic again, the Due Hours field appears blank.-This is wrong — it should display the last saved value instead. DONE",,,FUNCTIONAL,OPEN,Nishant Chorge Kumail Rizvi,Ready To Test,,https://ibb.co/Z1hh41B9,VERIFIED,
HELPDESK >>Help Topics >> action >> view,"Missing Notification Activities Section
- The Notification Activities section is not visible in the View Help Topic screen.
- This section should be present to show related actions or updates.

 Incorrect Visibility Label Display DONE
- The help topic is marked as Private, but instead of showing “Private” under the Visibility label, it shows the member’s name.
- This is incorrect — it should clearly show “Private” to indicate the correct visibility status.
- helptopic name should not required to be editable 
- please check the screen shot ",https://prnt.sc/wgl-55ZO8cXc,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,VERIFIED,
HELPDESK >>Help Topics >> new help topic ,issue is removed by society team form here ,,,FUNCTIONAL,OPEN,,Ready To Test,,,PENDING,
HELPDESK >> Canned Responses>> ADD Canned Responses," Inactive Canned Responses Not Visible
When a new canned response is saved with Inactive status: It gets saved, but does not appear on the Canned Responses page.
This is incorrect — all saved entries (including inactive ones) should be listed. 
 Missing Delete Action
There is no delete button or action available to remove a canned response.
- delete confirmation message is missing ""Do you want to discard this issue?"" ""yes"" ""cancel""


Missing Activities Section in View
When viewing a canned response: The Activities section is not shown.
This section should be present to show history or logs related to the canned response.
-- helptopic name should not required to be editable 
-- compare the UI of both the old and new societies 
-- newly created canned responses does not save the status ( active/ inactive)
-- plese check the 2nd screenshot 
If a user tries to save a canned response with Active status: The save fails and the Canned Responses page shows “No data found”.
This is incorrect — active entries should be saved and displayed properly.

Edit – Inactive Status Not Reflected
When editing a canned response: If the status is changed from Active to Inactive and saved, the updated response does not appear.
Instead, the system shows the message “No data found”, which is wrong — it should display the updated response correctly.","https://prnt.sc/EPBic6SWcxjE


https://prnt.sc/jSpcy82KLHuB",https://prnt.sc/H-VR7TWZyloy,FUNCTIONAL,OPEN,Kumail Rizvi Bilal Momin,Ready To Test,,,VERIFIED,
"Helpdesk >> Escalations
Escalation Details","Missing Activities Section in View
When viewing a Escalations details : The Activities section is not shown.
This section should be present to show history or logs related to the Escalations.
not able to view the new escalations- please check the screenshot ",https://prnt.sc/SSBl__ikxnQR,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,VERIFIED,
Helpdesk >> Escalations>> edit ," Improper Labels Instead of Actual Members
In the “Escalate To” dropdowns:
The system shows only labels like “Member” or “Staff” and user is able to select these label which is incorrect.
still many of the actual member is missing from dropdown 
Incorrect Display After Saving- After saving the form:
-The values shown under “First Escalate To”, “Second Escalate To”, and “Third Escalate To” columns are wrong or mismatched.
-They do not reflect the actual selections made by the user, leading to data inconsistency. DONE",,,FUNCTIONAL,OPEN,Kumail Rizvi Bilal Momin,Ready To Test,,https://ibb.co/KpX2f5NJ,VERIFIED,
Helpdesk >> Escalations>> Add Escalation,"Incorrect Label Text – ""Delay By""
-rename it to ""if delay by""

Missing Info Message for “if Delay By” label 
-There is no informational tooltip or message to explain what “ if Delay by” labels means.
- An info message should be added to help users understand this field
- on esclation view - it does not show the status "" active ""; in view it also show incorrect ""first esclate to , 2nd esclate to and 3rd easclate to ""
 - - not able to save the new  Escalation DONE
Page Does Not Auto Refresh After Save : After a user adds the first escalation and clicks Save:
- the entry gets saved, but it does not appear automatically on the Escalations page.
- The user is forced to manually refresh the page to see the new escalation
 
Invalid Input Accepted in “Delay By” Field
-The “Delay by” field wrongly accepts alphabetical characters and saves them.
-This is incorrect — it should only accept numeric values (e.g., number of hours).
- Proper validation needs to be added to prevent non-numeric input.",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,https://ibb.co/MxZP1jsL,VERIFIED,
INCOME,,,,,,,,,,,
Income>>Maintainance Dues >>maintainance receipt ,"if payment mode is cheque then user can select the future date also (DONE )
if payment mode is cheque then user can select the future date for receipt now only able to select past and present date ",,,,,,,,,VERIFIED,
Income>>Maintainance Dues >>generate invoice >> preview invoice  ( pause ) ,change message,,,,,,,,,,
seelct >> edit member name ,hide edit member name ,,,,,,,,,,
Income>>Maintainance Dues >>select member,"Download Invoice Not Working - on downloading shows no data ( which is incorrect )
When clicking “Download Invoice”, an error message appears: ""The route api/admin/common-billing/downloadInvoice could not be found.""

Edit Member Name Leads to 404
When clicking “Edit Member Name”, the user is redirected to a page that says: ""404 Not Found"" - not sure about the fucnationality its working properly or not 

Preview Invoice Leads to 404 
When clicking “Preview Invoice”, it also shows page in loading state for more plese check the screenshot :- 

downlaod invoice - it shows me success messsage but nothing is downloaded.",https://prnt.sc/qyadDgvNrRM0,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,NOT DONE,Only Test 2nd point 
Income>>Maintainance Dues >>action >> generate invoice ," Preview Invoice
When clicking “Preview Invoice”, the page keeps showing a loading spinner
The preview never completes or displays the invoice.
please chekc the screenshot that shows this issue in more detail.

Generate Invoice – Not Working ( now its funcational behaviour is incorrect)
Clicking “Generate Invoice” does not trigger any action or result.
The invoice is not generated, plese check the  screenshot that highlights this issue.

please check the screenshot - duplicate labels - but UI is still improper ","https://prnt.sc/p1MVOC6X_HN8


https://prnt.sc/239pMfiTtOZC

https://prnt.sc/TAe29pB67nZD",,FUNCTIONAL,OPEN,,Partially Done,,,NOT DONE,
Income>>Maintainance Dues >>action >> download  invoice ,"Download Invoice – Network Error (incorrect excel download ) and downloaded excel should be invoice number and date instead of ""download"" 
When clicking “Download Invoice”, it fails with the error message: ""Network Error""
please check the screenshot ",https://prnt.sc/FqHyv0UttDbF,,FUNCTIONAL,OPEN,Suraj Jamdade Nishant Chorge,Ready To Test,,,NOT DONE,Download ho rhi hai pr data nahi dikh raha
Income>>Maintainance Dues >>action >> view all  invoices ," Default Option Display
Instead of showing a ""Select Option"" dropdown or link button by default, the system should: Auto-select and display “Member Invoices”
Because the data shown in the table below is already related to Member Invoices, this default view . 
 
Page 404 Errors on Switching Views
When switching from “Member Invoices” to “Member Unit Statement”, the following error appears: ""404 Page Not Found ⚠️ – We couldn't find the page you are looking for.""
Similarly, switching back from “Member Unit Statement” to “Member Invoices” also results in the same 404 error. 

incorrect switching behaviour 
back bautton is not working

if member paid all its dues "" view all invoices "" icon got disable it should required to be enable everytime - DONE
while switching to member receipt page "" new reciept "" and ""back"" button is missing  on this page ",,,FUNCTIONAL,OPEN,Bilal Momin Shweta Danawale Kumail Rizvi,Ready To Test,https://prnt.sc/SHpDkGtlFdi7,"https://prnt.sc/WrVsHh-gleYy



https://ibb.co/PGR7bNq5",VERIFIED,Swtiching from Mumber Invoices to Member Unit Statement works as expected.
Income>>Maintainance Dues >>action >> view all  invoices >> member invoices  >> view invoice ,"Edit Member Name – Page Not Found :- When selecting “Edit Member Name”, the user is redirected to a 404 error page:
""This page could not be found.""

Credit Balance Missing - The credit balance is expected to be displayed at the top of the table, but it is:Not visible; it should required to be clickable and user should redirect to view transaction page 

Data Mismatch – Old vs New Society :- Under “View”, the displayed data for Old Society and New Society: please check the screenshot for more details ( compare both the ui you will find the difference )
invoice total , grand total and balance due are incorrect ",https://prnt.sc/pRVFvKZg1mp-,https://prnt.sc/Tqts5h6824FY,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,NOT DONE,pdf k mismatched data suraj sir dekh rhe hai 
Income>>Maintainance Dues >>action >> view all  invoices >> member invoices  >> send notification ,"Send Notification – Button Not Working
When the user clicks the “Send Notification” button: Nothing happens
There is no confirmation message, error message, or system response",,,FUNCTIONAL,OPEN,Suraj Jamdade,,,,NOT DONE,
Income>>Maintainance Dues >>action >> view all  invoices >> member receipts ,"Member Receipt – Filter Not Working ( when apply filter by  ; without filter by its working ):- In the “Member Receipt” section, when the user uses the search by filter: 
- If they enter a specific Invoice Number, the system is expected to show only the matching record Instead, it displays all records, ignoring the selected invoice number

receipt number should be clickable - DONE",https://prnt.sc/yM-1V1EoHPdQ,https://prnt.sc/mvTxnM3ddnBN,FUNCTIONAL,OPEN,Kumail RizviShweta Danawale Nishant Chorge,Ready To Test,,https://ibb.co/Z6zjq2wG,VERIFIED,In member receipt table there is only 1 section i.e; receipt number.  for this check attached screenshot
Income>>Maintainance Dues >>action >> view all  invoices >> member receipts >> action >> payment reversal ,"Incorrect Redirect After Payment Reversal :- When a user performs a payment reversal from the Member Receipt page:
The system redirects the user to the Payment Tracker page in the Expense module, This is incorrect behavior. The user should remain on the same Member Receipt page after completing the reversal.

Reversed Entry Still Visible :- After the payment is reversed: The reversed entry is still displayed on the Member Receipt page is also incorrect .The reversed entry should either: Be clearly marked as reversed.

please check the screen shot 

not able to reverse the payment show error message ""network error "" ; for more details please check the screenshot DONE

payment reversal popup label UI Is incorrect please check the screenshot :- https://prnt.sc/y8dscmm2Eaff","https://prnt.sc/HcKGCp8pI2k-


https://prnt.sc/suv5E23gIFlk",,FUNCTIONAL,OPEN,Bilal Momin Shweta Danawale Kumail Rizvi,Ready To Test,,"https://prnt.sc/4OckpXVpwCzT (Total Due before payment reversal)

https://prnt.sc/HLY7ySQcFdYm (Paymenmt reversal success)

https://prnt.sc/WPH5sbb1_AMq (Total Due after payment reversal)",VERIFIED,
Income>>Maintainance Dues >>action >> view all  invoices >> member receipts >> action >> send notification ,"Send Notification – Button Not Working
When the user clicks the “Send Notification” button: button goes to loading state and after some time gives network error toast message
There is no confirmation message, error message, or system response",,,FUNCTIONAL,OPEN,Suraj Jamdade,Partially Done,,,NOT DONE,
Income>>Maintainance Dues >>action >> add receipt ,"Add Receipt – Invoice Selection and View Icon Missing
- When the user clicks the Add Receipt button and is redirected to the Invoice Due page, all invoices should be auto-selected by default. Currently, the page shows all invoices unselected, which is incorrect.
Also, the ""View"" action icon is missing and should be visible for users to preview invoice details.

not able to add receipt shows errror messgae please chekc the screenshot ; shows error message ""count(): Argument #1 ($value) must be of type Countable|array, string given""
write off functaionality is incorrect in add recipt form 
shows incorrect late payment charges 
","https://prnt.sc/x6rvJDiJPOX9

",,FUNCTIONAL,OPEN,Shweta Danawale Nishant Chorge,Ready To Test,,,NOT DONE,
Income>>Maintainance Dues >>action >> add outstanding ,"Missing Unit Label: The unit label is not displayed on the screen.

Missing Note Section: The Note section is missing on both: The Outstanding screen: The Advance screen (radio button options) 

Outstanding Amount Not Reflected in Total Dues : After saving an Outstanding Amount, a success message appears. However, the amount is not added to the Total Dues column.

Advance Amount Not Reflected: After saving an Advance Amount, a success message is shown. But the amount is not displayed or updated in the Advance column.

add intreset amount place holder it shows enter grace period which is incorrect 

advance amount is autoreflected in ""principle amount"" field of outstanding radio button 
once user add outstanding its total should reflect in total due column ",,,FUNCTIONAL,OPEN,Suraj Jamdade Kumail Rizvi,Ready To Test,,,VERIFIED,
Income>>Maintainance Dues >> new receipt >> is suspense receipt ?,"Unnecessary TDS Button Displayed - When the user selects ""Suspense Receipt"", the TDS button appears in the amount field — this should not be shown.

Amount Field Clears on Date Selection - When the user selects a date, the amount field gets cleared automatically — this behavior is incorrect.

Payment Field Clears on Entering Receipt Note - When the user types a Receipt Note, the Payment field gets cleared, which is not expected.

Save Fails Due to Missing Payment Mode - Even after entering all fields and clicking Save, the form shows an error saying “Payment Mode field is missing”.The issue is valid — the Payment Mode field is actually missing from the form. 


TDS BUTTON is non clickable which is INCORRECT


after saving shows error message  "" unit key not found """,https://prnt.sc/0WHyh-txXImM,,FUNCTIONAL,OPEN,Nishant Chorge,Ready To Test,,,VERIFIED,Done from backend as well
Income>>Maintainance Dues >> new receipt ,"Error While Saving Maintenance Receipt: When trying to save a maintenance receipt using Cash or EFT as the payment mode, the system shows a “Network Error” message.
The form cannot be submitted successfully.
Please check the  attached screenshot.

data mismatch for same member in both old and new society 

TDS BUTTON is non clickable which is INCORRECT
In member dropdown field show buiding and unit number of member also ","https://prnt.sc/N8aex8ffnKpO

https://prnt.sc/DFLOy1lFF3Tk",https://prnt.sc/1AdmcvH8Im5-,FUNCTIONAL,OPEN,Shweta Danawale Nishant Chorge,Ready To Test,,,VERIFIED,
Income>>Maintainance Dues >> generate bulk invoice ,"generate bulk invoice-  Invoice Date, Due Date, Invoice Period, To date and Last Invoice Period should required to be autopopulated
 
Preview and Generate Functionality Not WorkingThe Preview Invoice and Generate Invoice buttons are not functioning — clicking them does not perform any action.

Please check the screenshot",https://prnt.sc/JI63h2_n2H3I,,FUNCTIONAL,OPEN,Bilal Momin,IN PROGRESS,,,NOT DONE,"Bilal, use the following API 

income-details/previewWithGenerate/{unit_id}"
Income>>Maintainance Dues >> generate single invoice ,"Last Invoice Period Not Displayed: The Last Invoice Period is missing from the invoice popup.

Incorrect Invoice Period To Date:- The “Invoice Period To Date” is displaying an incorrect value. 

Preview and Generate Buttons Show Error:- Clicking Preview Invoice or Generate Invoice triggers an error message instead of functioning as expected. please refer to the attached screenshot (",https://prnt.sc/NGIw-wF3NGAm,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,Partially Done,,NOT DONE,Data is proper preview please check Suraj Jamdade
Income>>Maintainance Dues >> export excel ,"The Excel file is not showing the total for the following columns: Total Due (₹), Advance (₹) These columns should display a sum at the bottom, but it is currently missing.

Total Due (₹), Advance (₹)  are incorrect while comparing with old and new onesociety for same company ",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,NOT DONE,
Incomes>>Incidental Bills>> new reciept ,"new receipt button is not working :- please check the screenshot ( so i am unable to check this whole functaionality) DONE
TDS  button :- tds amount should not be grater then the due amount 
user can select future receipt date if payment mode is cheque only DONE",https://prnt.sc/cMz56ABopDMS,,FUNCTIONAL,OPEN,Shweta Danawale Bilal Momin,Ready To Test,,,VERIFIED,
Incomes>>Incidental Bills>> add incedental bill ,"Unable to Add Incidental Bill:- When trying to add an incidental bill, the system shows an error message and the bill cannot be saved. please refer to the attached screenshot DONE
- to select all unit at once all selction  section missing form dropdown
- from date to date validation is incorrect in this form 
- shows success message ""common bill added successfully "" instead of ""Email and SMS has sent successfully."" DONE

show all in member name if unit selected all 
email and sms is not received by user ",https://prnt.sc/Wg6ZV0F5HNQ5,,FUNCTIONAL,OPEN,Shweta Danawale Bilal Momin,Ready To Test,,"https://prnt.sc/ajAv_JC__2GO

https://prnt.sc/PPtY4ZCVXA-O",NOT DONE,
Incomes>>Incidental Bills>> export excel ,the excel file is showing the incorrect column name  ( incorrect excel ) ,,,FUNCTIONAL,OPEN,Suraj Jamdade Kumail Rizvi,Ready To Test,,https://ibb.co/XfWXhFN6,,
Incomes>>Incidental Bills>>view all invoices >> downlaod excel ,account name is missing in pdf / excel,,,,,,,,,,
Incomes>>Incidental Bills>>action >>new invoice ," Incorrect Unit Number Label:- The label shows as “Unit Number Hello”, which is incorrect. It should simply display “Unit Number”. 

Incorrect Dates Displayed:- The following dates are showing incorrect values: Due Date and Period From Date

Incorrect Invoice Amounts:- The following fields are not displaying accurate values:,Invoice Amount, Grand Total and Balance Due

Error While Saving Incidental Bill:- Unable to save a new incidental bill — system shows the following error:"" Attempt to read property 'ledger_account_id' on null"" Please refer to the attached screenshot

Delayed Payment Charges ( on amount 5777 ) label should be look like this 
bill date mismatched in old and new society for same member ; please compare the attached screenshot  of both the societies 
 "" instead of ""Email and SMS has sent successfully.""","https://prnt.sc/VbXkX3IzvGf2


https://prnt.sc/r1PTiKr0TNyz",https://prnt.sc/tWI9NHBYQKmE,FUNCTIONAL,OPEN,Shweta Danawale Bilal Momin,Ready To Test,,"https://prnt.sc/k7tm5kyl1wF8 (Old Society)

https://prnt.sc/DuCiQLhxbTuh (New Society)",,Have to sit on this with Frontend team - Shweta
Incomes>>Incidental Bills>>action >>view all invoices >> view invoice ,"please check the screenshot 

total old and new society for same member ; please compare the attached screenshot  of both the societies
","https://prnt.sc/RekBzzIjWMnm

https://prnt.sc/KEHhRubW8Wfs",https://prnt.sc/h-cRQ8t6DhDj,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,,
"Incomes>>Incidental Bills
incomes >> click on invoice number link ","please check the screenshot 1
 please check the screenshot 2","https://prnt.sc/Gl7VgbK7Do_r

https://prnt.sc/3DovB8BL3vMh",,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,,
Incomes>>Incidental Bills>>action >>view all invoices >> add payment receipt ,"Incomes>>Incidental Bills>>action >>view all invoices >> add payment receipt 
while clicking on tds button it shows write off field which is incorrect ; please check the screenshot",https://prnt.sc/KEHhRubW8Wfs,,FUNCTIONAL,OPEN,Nishant Chorge Shweta Danawale,Ready To Test,,"https://prnt.sc/v7F0oNQh0s05 (Incidental payment done)

https://prnt.sc/85mHQtdS19BP (Generated receipt)",VERIFIED,
Incomes>>Incidental Bills>>action >>view all invoices >> send notification ,"Unable to Send Notification:- When trying to send a notification, the system shows an error message and the action fails. please check the screenshot ",https://prnt.sc/LuKPT4Ki6rdC,,FUNCTIONAL,OPEN,Suraj Jamdade,PENDING,,,,"Use the following API 

common-billing/sendNotification/{invoice_number}/{id}"
Incomes>>Incidental Bills>>action >>view all invoices >>cancel invoice," Invoice Cancellation Fails:- When attempting to cancel an invoice, the system shows the error: ""No record found for this ID"" Because of this, the invoice cannot be cancelled. DONE

after cancelling the invoice page should required to be redirected on incidental bill listing page now it incorrectly redirects to the maintanance due page DONE",,,FUNCTIONAL,OPEN,Bilal Momin Shweta Danawale,Ready To Test,,"https://prnt.sc/CCbZW2gE84vj (Before deleting)

https://prnt.sc/DMHMVAfE7Ztq (After deleting redirect to incidental bill page)",VERIFIED,
Incomes>>non member  Bills,"period not displayed DONE
toatal due wrong display DONE",,,FUNCTIONAL,OPEN,Shweta Danawale,Ready To Test,,https://prnt.sc/D8QE91gy90AU (total due and period is displayed),,
"Incomes>>non member  Bills>> invoice link 

>> add receipt ","Invoice Number Link Error:- When the user clicks on the Invoice Number, it leads to a 404 Page Not Found error. The link should redirect to the correct invoice details page. DONE

Incorrect Information in 'View Invoice':- under the Action > View Invoice option:The Invoice Date and Invoicing Period are being displayed incorrectly. DONE

 data mismatched in old and new society for same member ; please compare the attached screenshot  of both the societies 

please check the second screenshot 

when user click on TDS button the amount filed should required to clear 
if payment mode is selected EFT  show tranasction refrence feild instead of cheque number 
add this validation on tds amount filed ""Please enter amount less than or equal to receipt amount"" instead of ""TDS Amount must be equal to Payment Amount"" 
address is not displayed on pdf
note content  is incorrect on pdf  
account name is missing 
ifsc code is missing","https://prnt.sc/xyVxXw6TLnWk



https://prnt.sc/x9EdoLZKwojC",https://prnt.sc/vyiPlfnieTt2,FUNCTIONAL,OPEN,"Shweta Danawale Nishant Chorge 
Suraj Jamdade",Ready To Test,,,,But Nishant Chorge please address why 404 issue is happening
Incomes>>new non member  Bills," Missing Optional Section for Payment Receipt:- The optional section for Payment Receipt is not displayed separately, making the form less user-friendly.

GSTIN Number Should Not Be Mandatory:- The system currently requires the GSTIN Number, but this field should be optional, not mandatory.

Incorrect TDS Validation Logic:-The form cannot be saved unless the TDS amount equals the total amount, which is incorrect behavior.The user should be allowed to:Enter a TDS amount less than the total amount.Not exceed the total amount in the TDS field. ( now TDS button is not showing on this page )

if user select payment mode EFT - if populate field enter your cheque number which is incorrect  ; please check the screenshot

from date to date logic is incorrect 

incorrectly save the form 
not able to save the form shows error message :- please chekc the screenshot 
on applied applicable tax total tax not reflecting or calculate 

reciept label should be optional

GSTIN Number should required to be optional now its mandatory which is incorrect 
In receipt date field user  should be able to select future date if payment mode is cheque only . now user able to select future date on paymnet mode cash and EFT whcih is incorrect

on saving forms shows error message ""Undefined variable $paymentStatus""","https://prnt.sc/BzRGm5kNXBBI


https://prnt.sc/1My5_kkeoiRu
",,FUNCTIONAL,OPEN,Bilal Momin Shweta Danawale,Ready To Test,,https://prnt.sc/FIlz30AbzlOI,,Need to discuss with mahesh regarding TDS validation
Incomes>>receipt tracker >>action >>cleared ,"Cheque Clearance Issue:- When attempting to clear a cheque, the system displays the error message:""No network error"" As a result, the cheque clearance fails and the action cannot be completed.",,,FUNCTIONAL,OPEN,Bilal Momin Kumail Rizvi,Ready To Test,,https://ibb.co/4n4dg2bx,VERIFIED,
Incomes>>receipt tracker >>action >>bounced,"Cheque bounce Issue:- When attempting to bounce a cheque, the system displays the error message:""No network error"" As a result, the cheque bounce fails and the action cannot be completed.

 Incorrect Status After Bounced Cheque:- In some cases, the system shows a success message, but the cheque status is incorrectly updated to ""Cleared"".It should instead display the correct status: ""Bounced"".",,,FUNCTIONAL,OPEN,Bilal Momin Kumail Rizvi,Ready To Test,,,VERIFIED,Just need to change message
Incomes>>receipt tracker >>action >>edit ,"Mandatory Fields Not Auto-Populated:- The following fields are not auto-populated, even though they are required:
Transaction Reference (Cheque Number)*, Bank Name and Branch Name - DONE

Write-Off Button Missing:-The Write-Off button is not visible on the screen, making it impossible to apply write-offs. - Done
remove net amount field
Missing Write-Off and TDS Amount in Receipt Tracker:- In the Receipt Tracker listing, the following columns are not showing data: TDS Amount (TDS) and Write-Off Amount (W/O) - Done ",,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,,
Incomes>>receipt tracker >>action >>reversal ," Unable to Reverse Payment:- When attempting to reverse a payment, the system displays the error: ""Unable to complete transaction – no invoice found"" As a result, the payment cannot be reversed. - DONE",,,FUNCTIONAL,OPEN,Bilal Momin Shweta Danawale,Ready To Test,,,,
Incomes>>receipt tracker >>action >>send notification ,send notification functaionality is not working ,,,FUNCTIONAL,OPEN,Suraj Jamdade,PENDING,,,,
Incomes>>advances >> non member ,"showing duplicate table ; please check the screen shot 
view - show extra column adjust against which is not required  - DONE
view activity logs :- Created by, Created Date, Updated by and Updated Date section is missing - DONE
activity timeline is missing shows error message :-Error: Timeline component requires apiURL - DONE",https://prnt.sc/RwC9M01yr0zq,,FUNCTIONAL,OPEN,Shweta Danawale,Ready To Test,,,,This error shown because of old build
Incomes>>advances >> new advance >> member ," Member Name Not Searchable:- When trying to search for a member name, the dropdown shows:""No member details""
This makes it impossible to select or assign a member.

Error When Saving Member Form with Refundable Types:- When saving the Member form with type set to ""Refundable"" or ""Refundable and Adjustable"", the system shows an error:""used_for is required for unit context.""This error is incorrect, and the form fails to save.",,,FUNCTIONAL,OPEN,Nishant Chorge Shweta Danawale,Ready To Test,,,VERIFIED,
Incomes>>advances >> new advance >> non member ,"Incorrect Member Selection for Non-Member Account: When the account type is selected as ""Non-Member"", the system still allows the user to select member names from the dropdown. This is incorrect behavior — the dropdown should:Only display non-member names. Only allow selection of non-members

Error When Saving Member Form with Refundable Types:- When saving the Member form with type set to ""Refundable"" or ""Refundable and Adjustable"", the system shows an error:""used_for is required for unit context.""This error is incorrect, and the form fails to save.

after saving non member new advance it should redirect to non member page now its redirecting to member tab. DONE
",,,FUNCTIONAL,OPEN,Nishant Chorge Shweta Danawale,Ready To Test,,,VERIFIED,
Incomes>>advances >> new advance >> member >> refund money,"Refundable Balance Not Auto-Populated:- The field “Refundable Balance:” is showing the placeholder {{total_refundable}} Rs instead of the actual amount.This is incorrect and causes confusion. DONE

Incorrect Field Reference:- The system is displaying an incorrect field name: total_refundable, instead of fetching and showing the actual refundable amount.As a result, users are unable to process refunds. DONE

Missing Fields for Bank Refunds:- When selecting “Refund Payment by Bank”, the following mandatory fields are not displayed:Date, Transaction Reference (Cheque Number), Narration Without these fields, the refund functionality is incomplete and not usable.",,,FUNCTIONAL,OPEN,Bilal Momin Shweta Danawale,Ready To Test,,,VERIFIED,
Incomes>>Billable Item,"Data mismatched DONE
Not able to save the billable item. when select period more than 1 then it will insert only 1 record instead of multiple record DONE
on edit it does reflect the saved details ",,,FUNCTIONAL,OPEN,Shweta Danawale,Ready To Test,,,VERIFIED,
Incomes>>credit note,"Add global search fileration DONE
not able to save the credit note shows error message particular data is required DONE

validation is missing ""Rectification amount should be less or equal than particular amount!""
",,,FUNCTIONAL,OPEN,Nishant Chorge Shweta Danawale,Ready To Test,,,VERIFIED,
EXPENSE,,,,,,,,,,,
"Expense >>Purchases/Expenses>> voucher number link , bill number link and view icon ","do not show ""Vendor Bill Particulars"" and "" Payment Transactions"" table on vendor bills details page DONE
attachment point ",,,Ui,OPEN,Nishant Chorge,Ready To Test,,https://prnt.sc/d6HB8C6P7DmG (remove the table if data not present),,
Expense >>Purchases/Expenses>>pay bill ,"note is missing :- Bills will be claimed based on the priority DONE
shows incorrect adjustable balance and cash in hand balance DONE 
write off validation is missing ;- Please enter Sum of payment amount and writeoff amount less than or equal to total due of selected bill (420) and incorrectly pay the bill 
reset button missing ",,,,,,,,,VERIFIED,"1) As discussed earlier pay bill flow set as same as income section
2) As discussed earlier wrong bank balance and adjustable balance shown in old society. But correct display in new society."
Expense >>Purchases/Expenses>>cancel voucher ,voucher number is not autopopulating in popup ,,,,,,,,,,
Expense >>Purchases/Expenses>> new payment ,"Count mismatched of billable item
not able to save the billable item. select more than 1 preiod then it will stored only one record insted of multiple record
shows incorrect adjustable balance and cash in hand balance DONE",,,FUNCTIONAL,OPEN,Shweta Danawale,Ready To Test,,,VERIFIED,As discussed earlier wrong bank balance and adjustable balance shown in old society. But correct display in new society.
Expense >>Purchases/Expenses>> new cash purchase ,once user created new cash purchase it shows vendor name pettty cash - need to discuss,,,,OPEN,Shweta Danawale,Ready To Test,,,VERIFIED,
Expense >>Purchases/Expenses>> new cash purchase ,"Cash Purchase Save Failure:- When trying to save a Cash Purchase, the system displays the error: ""Network Error"" As a result, the purchase is not saved. Please check the screenshot ",https://prnt.sc/oJ4RwyG05OpL,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,VERIFIED,
Expense >>Purchases/Expenses>> view ," Duplicate Section Display :- The ""Vendor Bill Particulars"" section is displayed twice on the screen, which is incorrect and may confuse users.

Missing Payment Details Table:- Voucher No, Bill No, Vendor Name, Payment Date, Bank Account, Reference No./Cheque No., Payment Mode, Comments  and Paid Amount (TDS) (W/O) table is missing 
for details please check the screen shot 

Missing Attachment Link in Vendor Details :- in the Vendor Details section, the attachment link is not visible.",,,FUNCTIONAL,OPEN,Nishant Chorge,Ready To Test,,,VERIFIED,
Expense >>Purchases/Expenses>> unapproved,"Missing Unapproved Entries in Purchases/Expenses:- Unapproved Purchase and Expense entries are not appearing under the ""Unapproved"" tab.",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,VERIFIED,
Expense >>Purchases/Expenses>> rejected ,"Missing rejected Entries in Purchases/Expenses:- rejected Purchase and Expense entries are not appearing under the ""rejected"" tab.",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,VERIFIED,
Expense >>Purchases/Expenses>> unapproved>>edit,"Issues in Edit Functionality for Unapproved Purchase/Expense:- GST Number Should Not Be Displayed. GST number is unnecessarily visible on the form during editing.This field should be hidden in the edit view.

Updated Data Not Saving :- When the user edits and updates the form, the changes are not saved.The system does not reflect the updated data, making the edit ineffective.

Broken File Icon Appears Without Upload :- If a scanned bill is not uploaded during vendor bill creation: The system still shows a broken or empty file icon ( [ [] ).This is misleading, as no file was actually uploaded.

Incorrect Vendor TDS Rate :- The form shows Vendor TDS Rate as 0%, which is incorrect. The actual TDS rate should be 25%, as per vendor configuration.

Overall Edit Functionality is Broken ;- The entire edit process is unreliable: Data doesn't save, Fields show incorrect values, UI elements are inconsistent.

please check the screenshot ",https://prnt.sc/rDtLF61l1o0T,,FUNCTIONAL,OPEN,Nishant Chorge,Ready To Test,,,VERIFIED,
Expense >>Purchases/Expenses>> rejected,"Move to Unapproved Tab"" Redirection Not Working:- When the user clicks on the “Move to Unapproved Tab” icon, the system: Does not redirect to the Unapproved tab. Instead, the user remains on the same page, which is incorrect behavior.
Expected behavior: The user should be redirected to the Unapproved tab immediately after clicking the icon.",,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,VERIFIED,
Expense >>Purchase/Work Orders,please check the screenshot ,"https://prnt.sc/8hSlz_eI9_wx
https://prnt.sc/nSGEwDvFPn9h",,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,https://prnt.sc/eg1uF3lDD3Xy (As per your 2nd screenshot issue resolved),VERIFIED,
Expense >>Purchase/Work Orders >> title ,"- on view title should not be clickable  DONE
PO and  WO title should required to clickable now while clicking on it it shows ""404 Page Not Found ⚠️ DONE
We couldn't find the page you are looking for."" which is incorrect. DONE
- on print incorrectly show rejected by - neeraj ( on view its rejected by NA) (This is print/export so suraj sir will check this)",,,,,Suraj Jamdade Bilal Momin,,,,,
Expense >>Purchase/Work Orders >> STATUS ( rejected by ) ,"- if status is rejected by it should show lables Approved By: No one has approved this PO.
Pending Approval: Action By All
Rejected: Poonam
Reviewed / Refused By
Reviewed By: No one has reviewed this PO.
instead of showing NA for all these labels DONE (Set all labels as per mentioned above)

- showing rejected by status diffrent on card different on view and different on print 
- if multiple approved by and rejected by assigned to any po and wo it only reflect one name out of these which is incorrect  - need to discuss",,,,,Shweta Danawale Bilal Momin,,,https://prnt.sc/cEIf_NrBQw_f (Set label when status is rejected),,
Expense >> new Purchase/Work Orders,"Missing Approver/Reviewer Options in Purchase Order:- In the current scenario, no actual users are assigned as Approvers or Reviewers.As a result, the dropdowns for ""Select Approvers*"" and ""Select Reviewers"" only display:Label options like ""Staff"" and ""Member""No actual user names appear for selection. Despite this, the user is still able to:Select just the label (e.g., ""Staff"")
and able to Save the Purchase Order — which is incorrect behavior.

not reflecting selected select approval , and select review on cards - need to discuss ",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,VERIFIED,
Expense >> new Purchase/Work Orders >> view ,"Missing Item Details in Purchase/Work Order View:- Item Details Not Displayed
On the view screen of the Purchase Order or Work Order, the following key fields are not shown:Item Name,Unit Cost (₹),Item Quantity,Total Cost (₹) . Please refer to the first screenshot. - need to discuss 

Order Detail Link Shows Blank Data:- When the user clicks on the “Order Detail Name” link:The page loads blank, with no data displayed. Please refer to the second screenshot DONE","https://prnt.sc/AfeUUwkqLDVh

https://prnt.sc/fc0uNKzGvFdd",,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,https://prnt.sc/Q3isGNYFh_Wd (If item details are present then it will display in item details table otherwise not),,
Expense >> new Purchase/Work Orders >> print ,"Print Functionality Not Working for Purchase/Work Order:- When attempting to print a Purchase or Work Order, the system displays the following error message: ""404 – Page Not Found ⚠️
We couldn't find the page you are looking for.""",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,VERIFIED,
Expense >> new Purchase/Work Orders," Incorrect Behavior in Rejected and Unauthorized Purchase Work Orders:- Checkbox Still Selectable for Rejected Orders
Users are still able to select the checkbox for Rejected Purchase Work Orders.This is incorrect behavior — rejected entries should be non-selectable/disabled.

Conflicting Toast Messages on Unauthorized Approval:- When a user tries to approve a Purchase Work Order not assigned to them: The system correctly shows the toast message: ""Not assigned to you""
However, it also incorrectly shows another toast message:""Already approved"" Only the ""Not assigned to you"" message should appear.

select and unselect behaviour of PO/ WO - need to be discussed ",https://prnt.sc/zgtelhzq8uA5,,FUNCTIONAL,OPEN,Shweta Danawale Bilal Momin,Ready To Test,https://prnt.sc/jzGObbp3cpiD (in old society),https://prnt.sc/sv17KWt63gh1 (In new society),,Need To Discuss 
Expense  >> Member Billable Purchases >> pay to all members ,not able to select the checkbox ; due to which not able to check the full functaionality of it ,,,FUNCTIONAL,OPEN,Bilal Momin Shweta Danawale,Ready To Test,,,,
Expense  >> payment tracker >> column >> paymnet amount ,if payment is 700 and write off amount is 52 shows 700 (52) ,,,,,,,,,,
Expense  >> payment tracker >> view / download,"Unable to Download or View Payment Tracker PDF:- When trying to download or view the Payment Tracker, the system shows the error: ""Failed to fetch PDF file""
Please refer to the screenshot 

debit account label:-  value is missing (view/ dowanload). (SURAJ SIR )",https://prnt.sc/HmODGeMSTJDq,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,There is pdf generating but showing access denied due to CORS error. Need to check with Devops team.,,VERIFIED,
Expense  >> payment tracker >> reverse ,"Missing Reverse Note in Table Listing:- The Reverse Note is not displayed at the top of the Payment Tracker listing table.This field required  to be clearly visible.

Unable to Reverse Payment:- The user is not able to reverse a payment — the action fails.Please check the provided screenshot ",https://prnt.sc/ZAJg_l4SdFS8,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,VERIFIED,
Expense  >> payment tracker >> edit ,"Payment Tracker Not Updating After Edit:- After editing a payment entry, the Payment Tracker is not updated. An error message appears, and the changes are not saved. please check the screenshot 
bank balance amount tool tip is missing 
Please enter Sum of payment amount and writeoff amount equal to total due of selected bill(7415) ""this validation is missing on write off amount  :- need to discuss write off amount logic ",https://prnt.sc/DKhY4j-StSdr,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,,
expense >>Vendor Advances>>view >> type refundable >>edit ," Incorrect Type Editing Behavior in Refundable Form:- If the type is set to “Refundable”, and the user tries to edit the form, the system: Allows changing the type to “Refundable and Adjustable”
Then, on saving, shows an error: ""Undefined array key 'use_credit_after'""
The “Type” field should be disabled (non-editable) if it was originally set to Refundable, to prevent invalid state changes.
Please refer to the screenshot .",https://prnt.sc/PG3Ng97jt83j,https://prnt.sc/awHhj0WQLrYF,FUNCTIONAL,OPEN,Bilal Momin Shweta Danawale,Ready To Test,,,VERIFIED,
Expense  >> payment tracker >> cleared / bounced ,"Please refer to the screenshot . DONE

bounce status -after bounce show status as bounced currently 
if user click on clear icon it shows error and cleared the cheque sucessfully ","https://prnt.sc/QShxHvMou8qt
https://prnt.sc/vXtKIa_KNsxc",,,OPEN,Bilal Momin,Ready To Test,,"https://prnt.sc/YoOqHCYDsd_7 (issue resolve as per 1st screenshot)
https://prnt.sc/NRf8SC2jtU4W (as per 2nd screenshot)",VERIFIED,
expense >>Vendor Advances>>view >> type adjustable >>edit ," Incorrect Editing of Adjustable Type and Save Error:- When the type is set to ""Adjustable"", and the user edits the form: The system allows changing the type to:“Refundable and Adjustable”Or “Refundable”On saving, the system shows an error:""Undefined array key 'use_credit_after'""
If the original type is “Adjustable”, the Type field “Refundable and Adjustable”Or “Refundable” should be disabled and not editable.
Please refer to the screenshot.",https://prnt.sc/NI7Yyd7Bcpr7,https://prnt.sc/WWGugdSnsoO-,FUNCTIONAL,OPEN,Bilal Momin Shweta Danawale,Ready To Test,,,VERIFIED,
expense >>Vendor Advances,"Table Entries Not Auto-Refreshing After Save:- When the user saves or updates data, the table does not refresh automatically. As a result, the user must manually refresh the page to see the latest entries or changes.
The table should automatically update and reflect the changes immediately after saving, without requiring a page reload.",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,VERIFIED,
expense >>Vendor Advances>> refund ," Refund Error on First Attempt for Vendors:- When refunding money to any vendor using Cash or Bank as the payment method for the first time, the system shows an error:""Invalid ledger ID"" (toast message)
This error prevents the user from completing the refund.However, if the user tries to refund again for the same vendor, the refund works correctly on the second attempt.
Expected behavior: The refund should work on the first attempt without throwing the “Invalid ledger ID” error.
please check the screenshot  DONE",https://prnt.sc/BH4UldIUOhxZ,,FUNCTIONAL,OPEN,Bilal Momin Shweta Danawale,Ready To Test,,,VERIFIED,
VENDOR,,,,FUNCTIONAL,OPEN,OPEN,,,,,
Vendor >> export PDF ,"PDF Functionality is not working - shows error "" Trying to access array offset on null""",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,,
vendor >> new and edit vendor ,"When the user clicks on ""New Vendor"", both the RCM and GST Number fields are displayed — which is incorrect.
Since the ""Is GST applicable?"" option is auto-selected to ""No"", only the RCM field should be visible by default.
vendor payment due date reflected wrong on edit ",https://prnt.sc/BhdvCqpHJvxE,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,VERIFIED,
Vendor >> Add principle ,"When a user adds a Principal Amount through either: ""To Be Paid"" or ""Advanced Pay"" the payment is processed successfully.
However, the “Add Principal Amount” field remains active even after the amount is added. This is incorrect — the field should be disabled once any amount has been successfully added,

advanceentry   show in advance column  and to be pay entry  show in due column ",,,FUNCTIONAL,OPEN, Bilal Momin Suraj Jamdade,Ready To Test,,,VERIFIED,
Vendor >> view all bills >> vendor bill >> view ( voucher number & bill number)," Duplicate Section Display :- The ""Vendor Bill Particulars"" section is displayed twice on the screen, which is incorrect and may confuse users. DONE
duplicate back buttons

Missing Payment Details Table:- Voucher No, Bill No, Vendor Name, Payment Date, Bank Account, Reference No./Cheque No., Payment Mode, Comments  and Paid Amount (TDS) (W/O) table is missing 
for details please check the screen shot ",https://prnt.sc/99VnnWEo1j-m,https://prnt.sc/rE9KKW8mW5Yr,FUNCTIONAL,OPEN,Bilal Momin Nishant Chorge,Ready To Test,,https://prnt.sc/UgpBkHJseU-S (Duplicate section issue resolved),VERIFIED,
Vendor >> view all bills >> vendor bill >> pay bill ,"Mismatch in Advance and Cash Balances Between Societies:- The values for: ""Your Adjustable Advance Balance"" and ""Your Cash in Hand Balance"" are not matching with the balances shown in the old society records. DONE

payment mode cash/ eft :-When selecting Cash / EFT as the payment mode for a vendor bill: The system shows a toast message saying “Submitting”. However, it does not display a success message, and the payment does not go through. As a result, the user is unable to pay the vendor bill using cash. DONE

Missing Validation on Bill Selection Before Payment:- Currently, the user is able to initiate a payment using Cheque mode without selecting any bill, which is incorrect. The system should display a validation error when no bill is selected. ""Please check at least 1 bill ""  DONE",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,VERIFIED,
vendor >> new vendor bill ,"Vendor with GST Number | Payment Mode: Cash | Billable to All Members: No
Scenario: Vendor amount is ₹165; user enters ₹5 in Write-Off and ₹5 in TDS.
Expected Results:
(a) User should be redirected to the Purchases/Expenses → Approved tab.
(b) In Bill Amount (TDS) column: should display 165 (5).
(c) In Amount Paid (Write-Off) column: should display 155 (5).
(d) Payment Status should be Paid.
Current Issue: Not following this expected flow.

Vendor with RCM Number | Payment Mode: Credit | Status: Unapproved | Billable to All Members: No:- Expected: Payment Status should be Unpaid. issue System is incorrectly showing status as Paid.

Vendor with RCM Number | Payment Mode: Credit | Status: Approved | Billable to All Members: Yes:- User should be redirected to Purchases/Expenses → Approved tab. Payment Status should remain Unpaid.System incorrectly shows status as Paid.

 Incorrect Amounts in Purchase/Expense Table:- The table is showing: Wrong Due Amount Wrong Payment Amount .This is misleading and needs correction for financial accuracy.

TDS Auto-Population Not Working :- When a vendor is created with TDS selected and saved, the TDS value should auto-fill in the ""Deduct TDS"" field while creating a bill. Issue: The selected TDS is not auto-populating.

Vendor with GST Number | Payment Mode: Cash :- Issue in Expense Account Field: Instead of showing the actual expense account, it shows the vendor’s name — this is incorrect. Issue in GST Field:Similarly, the GST field is also incorrectly displaying the vendor’s name instead of the correct GST account/label.

Round-Off Suggestion Missing :- When applying Round-Off, the system does not show any suggestions or options to select the round-off value.

Cluttered Vendor Bill UI :- The UI layout of the Vendor Bill screen is too cluttered and not user-friendly. Simplify the UI to match the cleaner layout used in the old society version.

Write-Off / RCM Functionality Not Working :- The Write-Off and RCM (Reverse Charge Mechanism) functionalities are not working as expected. These features either do not trigger any action or fail to affect the bill status/calculation properly.

vendor TDS amount editable ",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,VERIFIED,
vendor >> vendor bill >> credit >> gst vendor ," Vendor Bill Calculation & Round-Off Issues:- Total Bill and GST Not Updating on Amount Change
When the user edits the amount in the vendor bill form before saving: The Total Bill Amount and Vendor GST do not update automatically.This leads to incorrect billing values being saved.",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,VERIFIED,
ACCOUNTS,,,,FUNCTIONAL,OPEN,,,,,,
accounts >>download leadger ," Ledger Export Not Working:- When attempting to export the ledger, the system displays a ""Submitting"" toast message. However, nothing happens afterward — the ledger is not downloaded or exported, and no error message is shown",,,FUNCTIONAL,OPEN,Suraj Jamdade,,,,,The api for download ledger isn't present
accounts >>edit leadger ,"Ledger Edit View UI Mismatch:- The Edit Ledger view in the current system is not matching the view used in the old society interface. Please align the Edit Ledger view with the old society version 
please check the screenshot for comparison 

Financial Year :
01/04/2021 - 31/03/2022 is missing 
table structure is very confusing 
income type field is missing (Check 2nd screesnshot for this)
",https://prnt.sc/fMDRhHy-APYh,https://prnt.sc/NamyLqS0Z8Kh,Ui,OPEN,Nishant Chorge Shweta Danawale,Ready To Test,,"https://prnt.sc/vAmReYIxPZvi (Select group set in edit ledger form)

https://prnt.sc/8rkjd9jZpNyi (income type field is displayed)",,
accounts >>edit leadger>>put,Ledger Edit:- The Edit Ledger api not working properly,,,FUNCTIONAL,OPEN,Shweta Danawale Nishant Chorge,Ready To Test,,,,
accounts >>New Ledger,"New Ledger:- New Ledger creation post api not working properly

save & new , reset  and cancel button is missing 
opening balance should be optional ; now its mandatory which is incorrect 
if nature of group saved credit ; on edit it shows debit which is incorrect 
opening balance is added while creating new leader is not reflecting on edit leadger 
after saving user should redirect to account page instead of charts of account page ",,,FUNCTIONAL,OPEN,Shweta Danawale,Ready To Test,,,,
accounts >>export  leadger ,export excel functionality is not working (SURAJ SIR ),,,FUNCTIONAL,OPEN,Suraj Jamdade,PENDING,,,,
accounts >>list transactions,"UI is improper ; please check the screenshot 
",https://prnt.sc/c2HRsGcVOeLI,,FUNCTIONAL,OPEN,Bilal Momin Shweta Danawale,Ready To Test,,,,
account >> charts of account >> refersh / download ledger ,"
downlaod ledger :- its not working give error message ""404
Page Not Found ⚠️
We couldn't find the page you are looking for. (SURAJ SIR )",,,FUNCTIONAL,OPEN,Suraj Jamdade,PENDING,,,,
account >> charts of account >> asset >>Account Receivable >>edit  ledger,"Ledger Edit View UI Mismatch:- The Edit Ledger view in the current system is not matching the view used in the old society interface. Please align the Edit Ledger view with the old society version 
please check the screenshot for comparison 
- not able to edit the ledger shows error message ""The value entered is invalid. Please check and try again."" kindly fix this issue 
- if Amout Credit/Debit is credit it always shows debit instead of credit ",,,FUNCTIONAL,OPEN,Kumail Rizvi Shweta Danawale,Ready To Test,,,,Bilal Momin Have to check form as well.
account >> charts of account >> asset >>Account Receivable ,"Data Mismatch Between Old and New Society Tables
As shown in the screenshot, there is a clear difference in the view and values displayed in the tables of the old and new society. Please compare the views and data between the old and new society tables as shown in the screenshot.
Fix the data mismatch issue to ensure both views are aligned and complete.
do not show edit and delete icon in "" brought forward row "" action column 
clicking on leadger account link shows error message ""Attempt to read property ""parent_id"" on null"" kindly fix this issue 
while edit this "" counter entry "" ""to "" "" account type "" "" Transaction Type*"" and "" Narration*""  filed is not auto reflected kindly fix this issue 
not able to save the ""Edit Transaction Details"" form
not able to delete the  leadger account  as it shows the error message ""The route api/admin/transaction/delete/401 could not be found.
 
export:- export excel functionality is not working (SURAJ SIR )",https://prnt.sc/w6njq-3wsnjg,https://prnt.sc/pS10wu03Eifj,FUNCTIONAL,OPEN,Kumail Rizvi Shweta Danawale,Ready To Test,,,,"Checked this in old and now society, data seems to be persistent"
account >> group >> new group ,"Newly Added Group Not Appearing in New Society List:- When a new group is created in the new society, it is:Not showing up in the new society's group list. However, it does appear in the old society's group list, which is correct. DONE
it should required to be shown in both new and old society's DONE

 Improper Validation in New Group Form:- The Group Name field currently allows any input, even if it’s not relevant to the selected group type. The system should validate that the Group Name is appropriate and relevant to the selected group type, preventing unrelated or incorrect entries.
not able to change group status from inactive to active ",,,FUNCTIONAL,OPEN, Shweta Danawale Nishant Chorge,Ready To Test,,,,"Bilal Momin

Check the second point and mark your status."
account >> group >> new group >>edit ,"Edit Group Functionality Issues:- When a user tries to edit a newly created group, the system behaves inconsistently: Sometimes it displays “Select Group” as the selected option, which is incorrect. DONE
Other times, it fails to display the previously selected group at all.
The correct group type should be pre-selected and visible when editing an existing group. DONE",,,FUNCTIONAL,OPEN,Nishant Chorge Shweta Danawale,Ready To Test,,,,
account >> Bank Reconciliation Form,"Missing Validation on Closing Balance Field:- The ""Closing Balance"" is a mandatory field, but the system allows saving the Bank Reconciliation Form without entering it.
This is incorrect and should be properly validated. DONE

Incorrect UI Icon for Reconciliation Month :- In the Reconciliation Month section Instead of showing a cancel icon, it incorrectly displays a select checkbox. This is confusing  with expected UI behavior.

Missing Functionalities Compared to Old Society :- Please refer to the screenshot comparison between the old and new society. Several features and sections present in the old society are missing in the new version, including:
Missing Sections: Reconciled Opening Balance of April 2021, Opening Balance of April 2021 by Ledger and Bank Account section

Auto-selection on First Page Load Not Working:- On the Bank Reconciliation (Financial Year) page: When the user lands on the page for the first time, all entries should be auto-selected by default. Currently, they are not — user has to select entries manually.

Error on Clicking ""Concile"" Button:- When the user selects any entry and clicks the “Concile” button, the system shows a 404 error: ""Page Not Found ⚠️ – We couldn't find the page you are looking for.""


- closing balance doesnot reflect on bank reconcial for fianancal year form ; also Amount Not Reflected in Bank filed comes empty which is incorrect
-""When the user clicks the 'Reconcile' button, an error message appears saying 'This entry is already reconciled.' However, if the user clicks the button a second time, a success message is shown saying 'This entry reconciled successfully,' which is incorrect. The reconciliation should be successful on the first attempt.",https://prnt.sc/rCcJQ7rYyqqm,https://prnt.sc/92J4DHpfSvd5,FUNCTIONAL,OPEN,Nishant Chorge Shweta Danawale,Partially Done,,,,
account >> close bank account ,"Trial Account Closure"" & ""Create New FY"" Buttons Not Functional:- The “Trial Account Closure” and “Create New Financial Year” buttons are not working. Their functionality is not implemented, so no action is triggered when clicked.

Re-Calculate Next FY Opening Balance Button Not Working:- The “Re-calculate Next FY Opening Balance” button is currently non-functional. Its functionality has not been implemented, so clicking it does nothing.

Error on Scrolling Closed Account Page:- When scrolling through the Closed Account page, the system displays an error message.
Please check the screenshot for details of the error.",https://prnt.sc/vtQ9Dm7FpBGy,,FUNCTIONAL,OPEN,Bilal Momin Kumail Rizvi,Ready To Test,,,,
account >> investments,"Missing “Add Assets & Inventory Settings” in New Society:- For newly created societies, if the user has not set up the “Add Assets & Inventory Settings”, they are unable to make any investment.This setting functionality is completely missing in the new society setup.
Please refer to the screenshots of the old society, where this setting is available and functional. Implement the same “Add Assets & Inventory Settings” functionality in the new society setup to ensure feature parity.",https://prnt.sc/d54b2MSEV8DH,"https://prnt.sc/q35dUjKqzKaA

https://prnt.sc/Mk9CuRTlfrea",FUNCTIONAL,OPEN,,NEED TO DISCUSS,,,,This page comes at the time of society setup
account >> investments >> view ,"UI & Functional Issues in Financial Module:- Incorrect Status Color The “Active” status is not shown in green color as it is in other parts of the application. For consistency, it should appear in green across all pages. - DONE
 
Missing Brought Forward and Total Balance The system is not displaying: Brought Forward Balance, Total Balance .These are important for accurate financial summaries.- 
filter is not working - if view as monthly selected august 2025 and view as yearly selected - 2025- 2026 it does not show any details on but in old society it shows the full list . kindly check the old and new society screen shot for this point selected society "" futurescapetech pvt ltd.""

Missing Export Functionality :-There is no export option to download financial data or reports (SURAJ SIR )
shows incorrect value is brought forward 
also show toatal debit and total credit empty show 0 instead.
do not show edit and delete icon in "" brought forward row "" action column 
not able to delete the  leadger account  as it shows the error message ""The route api/admin/transaction/delete/401 could not be found.
back button is required on each page as in old society it opens in new tab but in new society it opens in same tab so back button is required 


UI Alignment Issues :- There are visible UI misalignments, affecting readability and user experience. Please refer to the provided screenshot for exact positioning and layout problems.",in new society it shows blank data,https://prnt.sc/8F4rR7LZmStM,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,,
account >> investments >> Add interest ," Missing Success Toast Message:- After adding interest, the system does not display a ""Transaction Successful"" toast message.
with  “Your latest balance is in rupee”
please check the screenshot of old society",,https://prnt.sc/a07kfvmV88vz,FUNCTIONAL,OPEN,Shweta Danawale,Ready To Test,,,,
account >> investments >> Add topup," Missing “Transaction Successful” Message:- After adding a top-up, the system shows a generic success toast, but it does not display the specific message:""Transaction Successful""

missing topup Message:- The message shows: ""Your latest balance is in rupee"" This is incomplete — it should show the actual balance value, e.g., follow the same screenshot of above point 

Popup Not Auto-Closing:- After the success message, the top-up popup remains open, which is not expected. The popup should auto-close once the top-up is successfully processed.",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,,
account >> investments >> NEW INVESTMENT ,"Add validation between the start date and maturity date.
Currently, users can select a past maturity date even when the start date is today, which is incorrect.
The maturity date should always be the same as or later than the selected start date. If the user selects a start date, the maturity date must not be earlier than that",,,,,,,,,,
account >> current asset >> bank account,"Export to Excel Not Available:- The Export to Excel functionality is missing — it has not been implemented.

Retry Bank Activation Button Not Working:- The “Retry Bank Activation for Settlement” button and  its functionality is not implemented.

Missing Default Bank Badges:- Badges for: Default Bank for Incidental Default Bank for Non-Member are not visible or not implemented.
  
 Missing Payment Gateway Selection :- The “Select Payment Gateway for Bank” dropdown is missing — users cannot assign gateways.

Bank Balance on Card Not Clickable:- The Account Balance amount shown on the card is not clickable. It should redirect to a detailed view, but that functionality is missing. - DONE

Not Showing All Bank Accounts :- Not all bank accounts are visible: This may be due to missing pagination or an error in bank data. As a result, only some accounts are shown. 

Missing “Activated for Settlement” Badge: - The badge indicating a bank account is activated for settlement is not shown. - DONE

Missing “Amount Received From” Input Field:- he field “Please enter amount received from” is missing or not implemented in the form. 

Missing “Activate Bank for Settlement” Button :- The “Activate Bank for Settlement” button is not displayed, so users cannot initiate settlement setup.

Missing “Retry Account Verification” Button:- The “Retry Account Verification” button is also not present or implemented, preventing users from retrying failed verifications.",,,FUNCTIONAL,OPEN,Bilal Momin,Partially Done,,,,Pending from shweta and suraj sir side
account >> current asset >> bank account>> withdraw cash,"account number filed label should be from account 

it does not show the success message ""Transaction successful
Balance of Cash In Hand is 27,143.00
Balance of SBI BANK-************ is 27.00""

after saving the withdraw amount it shows the "" submitting "" toast message contineously in submitting state",,,,,,,,,,
account >> current asset >> bank account>> deposite cash,"
it does not show the success message ""Transaction successful
Balance of Bilal Bank 1-********** is 0.00
Balance of Cash In Hand is 27,198.00

after saving the deposit amount it shows the "" submitting "" toast message contineously in submitting state",,,,,,,,,,
account >> current asset >> bank account>> cash operation button ,"
cash operation button is not required on this page",,,,,,,,,,
account >> current asset >>  edit bank account," Missing “Corresponding Ledger” Section:- In the Edit Bank form, the “Corresponding Ledger” section is not displayed.
This prevents users from reviewing or updating the linked ledger account. 

Incorrect Checkbox Behavior for Default Bank Settings:- The checkboxes show incorrect selections when editing: Default , Default Bank for Incidental and Default Bank for Non-Member .The pre-selected values are not accurate, and do not reflect previously saved data. 

Checkbox Updates Not Saved
When the user modifies these checkboxes and saves the form:The changes are not saved. The checkbox selections revert to previous state or remain unchanged.",,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,,
account >> current asset >> bank account,Export Functionality Missing:- The export feature has not been implemented yet. (SURAJ SIR ),,,,,,,,,,
account >> current asset >> cash account ,"Export Functionality Missing:- The export feature has not been implemented yet. (SURAJ SIR )

Cash Account Name Not Displayed:- The cash account name is not showing up where it should.

Missing Default Badge:- The default badge (which likely indicates a default account or setting) is not visible.

Ledger Balance Not Clickable:- The ledger balance should be clickable.When the user clicks it, they should be redirected to the corresponding ledger page . but it does not show any amount in Brought Forward, Total Debit, Total Credit and Total Balance column which is incorrect kindly fix this issue 

Cash Ledger Name Not Updating on Edit:- When editing a cash ledger, the ledger name is not updating after changes on cash account card",,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,,
account >> fixed asset >> assets >> new asset ,"Export Functionality Missing:- The export feature has not been implemented yet. (SURAJ SIR )
asset details shows status 3 instead of expired 
on edit it does not reflect the Asset Appreciation Value and Asset Depreciation Value",,,FUNCTIONAL,OPEN,Bilal & Suraj Kumail Rizvi,Ready To Test,,,,
account >> Financial Statements>> trial balance ,"Date and Period Filters Missing:- Filters for Select Years, Yearly, Monthly, As on Date, and As on Month are not available.The Show button is also missing or non-functional.

Refresh and Export/Print Buttons Missing:- The Refresh and Print/Export buttons  functionality is not implemented shows error message The GET method is not supported for route api/admin/accountsreporting/trial_balance/download/excel. Supported methods: POST.


Data Accuracy Issues:- Incorrect Opening and Closing Balances.The total opening and closing balances for each particular item are incorrect. A comparison between old and new society data for “Shweta Co-op Housing Society Ltd” shows that the grand totals do not match. (Refer to the screenshot for detailed comparison.)  DONE

UI/UX Enhancements Required:- Grand Totals Should Be Fixed and Highlighted:- The grand total for each particular should be frozen at the bottom of the table. It should also be highlighted in a different color. Currently, it only appears after scrolling, which is not user-friendly. - DONE

financial year and period should be auto selected once user redirect to trial balance ",https://prnt.sc/wxh5cZa1_5DS,https://prnt.sc/irySHJFIqGfe,FUNCTIONAL,OPEN,Kumail & Nishant Kumail Rizvi,Partially Done,,,,Discussion is pending
account >> Financial Statements>> cash flow ,"""Missing or Incomplete Functionalities

Date and Period Filters Missing:- Filters for Select Years, Yearly, Monthly, As on Date, and As on Month are not available.The Show button is also missing or non-functional.

Refresh and Export/Print Buttons Missing:- The Refresh and Print/Export buttons afunctionality is not working shows error message The route api/accountsreporting/cashflow/download/excel could not be found.
.

Data Accuracy Issues:- Incorrect total inflow , total out flow and net inflow. The total inflow , total outflow and net inflow  for each particular item are incorrect. A comparison between old and new society data for “Shweta Co-op Housing Society Ltd” shows that the grand totals do not match. (Refer to the screenshot for detailed comparison.)

UI/UX Enhancements Required:-  Totals Should Be Fixed and Highlighted:- The grand total for each particular should be frozen at the bottom of the table. It should also be highlighted in a different color. Currently, it only appears after scrolling, which is not user-friendly.

financial year and period should be auto selected once user redirect to cash flow 

total inflow , total out flow and total net flow lables is missing also not showing any "" total net flow "" kindly fix this issue 
also shows total inflow incorrect ",https://prnt.sc/0xhqpN8yacvn,https://prnt.sc/9JF9vBWfDhLE,FUNCTIONAL,OPEN,"Nishant Chorge, Kumail",Partially Done,,,,Need to discuss
account >> Financial Statements>> Income And Expenditure,"""Missing or Incomplete Functionalities
Tree View and Detail View Not Implemented:- The Tree View and Detail View features are either missing or not yet implemented.
financial year and period should be auto selected once user redirect to Income And Expenditure


Date and Period Filters Missing:- Filters for Select Years, Yearly, Monthly, As on Date, and As on Month are not available.The Show button is also missing or non-functional.

Refresh and Export/Print Buttons Missing:- The Refresh and Print/Export buttons  functionality is not implemented shwos error message The route api/accountsreporting/profit_and_lossT/download/excel could not be found.


UI/UX Enhancements Required:-  Totals Should Be Fixed and Highlighted:- The grand total for each particular should be frozen at the bottom of the table. It should also be highlighted in a different color. Currently, it only appears after scrolling, which is not user-friendly.",,,FUNCTIONAL,OPEN,"Nishant Chorge , kumail",Partially Done,,,,
account >> Financial Statements>> balance sheet ,"
Date and Period Filters Missing:- Filters for Select Years, Yearly, Monthly, As on Date, and As on Month are not available.The Show button is also missing or non-functional.

Refresh and Export/Print Buttons Missing:- The Refresh and Print/Export buttons functionality is not implemented shows error message ""The route api/accountsreporting/profit_and_lossT/download/excel could not be found.

Data Accuracy Issues:- Incorrect total . The total for  each year are incorrect. A comparison between old and new society data for “Shweta Co-op Housing Society Ltd” shows that the grand totals do not match. (Refer to the screenshot for detailed comparison.)

UI/UX Enhancements Required:-  Totals Should Be Fixed and Highlighted:- The grand total for each particular should be frozen at the bottom of the table. It should also be highlighted in a different color. Currently, it only appears after scrolling, which is not user-friendly.

Clickable Elements Missing in Liability Section:- In the Liability section: Sinking Fund should be clickable and redirect to its detailed ledger view of siniking fund . The Profit amount (234,778.07) should also be clickable, allowing the user to view its breakdown or ledger.

financial year and period should be auto selected once user redirect to Income And Expenditure",https://prnt.sc/R8QZ1ipTimSG,https://prnt.sc/eNW_q8tgNmVW,FUNCTIONAL,OPEN,"Nishant Chorge , Kumail",Partially Done,,,,Discussion is pending
account >> tax >> new tax,"Missing Tax Listing on New Edit Tax Page:- The tax listings are not visible on the new Edit Tax page. Please refer to the screenshot for a comparison between the old society and new society — the old society view shows the tax list correctly, while the new one does not.

edit and delete tax list is missing 

no able to save the tax",https://prnt.sc/meueIK9y3mJJ,https://prnt.sc/aArbF_7fdlQS,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,"This has new UI, instead of list we are showing it in different format.",,,
account >> tax >> view ,"Incorrect Page Title:- The ""Vendor Bill Details"" page is displaying an incorrect name. It should reflect the correct page title.

Missing Rate Column in Tax Rule Listing:- The Rate column is not visible in the Tax Rule listing, which is essential information and should be displayed.

Incorrect Status Displayed:- The status is showing as ""Past"", but it should be ""Active"" for current items. This is misleading.Empty and Improperly Displayed Class Details
The Class Details section is empty, even when data exists.

The UI layout of this section is also not proper — please refer to the screenshot.",https://prnt.sc/wpkYzON_j5h0,,FUNCTIONAL,OPEN,Kumail Rizvi BILAL,Ready To Test,,"https://ibb.co/q3YbBdJy

https://ibb.co/fzCqGYt0",,Class detail pending by frontend
account >> tax >> Tax Exemption,"Missing Validation in Rate Field:- In the Rate section, the system should validate that the ""Type"" selected is the same as the ""Tax Class"". This validation is currently missing and needs to be implemented.",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,,
account >> challans>> GST,Missing TDS Challan Setup Functionality:- The TDS Challan setup is currently missing or not implemented. Please refer to the screenshot of the old society where this functionality is present. The same setup should be implemented in the new onesociety .,,https://prnt.sc/UnlXbSSCTCCe,FUNCTIONAL,OPEN,,NEED TO DISCUSS,,,,
account >> challans>> new TDS  challan,"BSR Code Field Validation Missing:- The BSR Code field should be restricted to a maximum of 7 characters.
Currently, it allows more than 7 characters, which is incorrect and needs validation.",,,FUNCTIONAL,OPEN,Suraj Jamdade Kumail Rizvi,Ready To Test,,,,
account >> voucher >> voucher tracker ,"Issues with Voucher Tracking Functionality
Pagination Not Working:- Pagination is not functioning, and as a result, not all voucher tracking entries are visible in the listing. DONE

Voucher Tracker – View Button Error:- Clicking the View button in the Voucher Tracker triggers the following error message: ""The route api/admin/transaction/viewVoucher/472 could not be found."" 

Voucher Tracker – Download Button Error:- Clicking the Download button also shows the same error message: ""The route api/admin/transaction/viewVoucher/472 could not be found."" ",,,FUNCTIONAL,OPEN,Kumail Rizvi Suraj Jamdade,Ready To Test,,,,
accounts >>Vouchers>>Journal Voucher / CONTRA  Voucher /  payment voucher /,"Activities Section Missing:- The Activities section is not present in the current interface. It needs to be added or restored as per the previous version.
Currently, the ""Transaction Date*"" field only allows the user to select today’s date. This is incorrect behavior — users should be able to select past dates for valid transaction entries. The date picker should be updated to allow historical date selection.",,,FUNCTIONAL,OPEN,BILAL Kumail Rizvi,Ready To Test,,,,
accounts >>Vouchers>>multiple Voucher,"Activities Section Missing:- The Activities section is not present in the current interface. It needs to be added or restored as per the previous version.

Unable to Save – ""Transaction Date is Required"" Error:- When trying to save a multiple payment voucher, an error appears: ""Transaction date is required"" Please check the screenshot for reference — this needs to be fixed.

Reset Button Missing- The Reset button is either missing or not implemented on this form.",https://prnt.sc/nib-qFEYzbt6,,FUNCTIONAL,OPEN,"SURAJ , BIlal",Ready To Test,,,,
accounts >>Vouchers>>debit note ,"save Button – First Click Shows ""Network Error"" :- When the user clicks the Save button for the first time, it shows an error: ""Network Error"" However, on clicking the Save button again, the voucher is saved successfully.

Debit Note – Optional Ledger Field - When the ""Adjustable"" option is selected under the Debit Note radio button: The ""To Ledger/Credit"" optional field is not required Currently, this condition is not handled properly in the form.",,,FUNCTIONAL,OPEN,Bilal & Suraj,Ready To Test,,,,
SOCIETY WEBSITE ,,,,FUNCTIONAL,OPEN,,,,,,
society website >> profile ,"Tab Name Change Needed:- The tab currently labeled ""Basic"" should be renamed to ""Profile"" for clarity and consistency. - DONE

File Upload Issues:- The instructional text below the ""Choose File"" button is missing. It should say: (Only JPEG & PNG format images of maximum height 60px and width 200px are allowed) - DONE

The Choose File button design is improper, and the field does not display the selected file name after a file is chosen — this needs to be fixed. - DONE

Missing Fields:- The ""No. of Flats"" field is not visible and should be included in the form. The ""Website Subdomain*"" field is missing, and instead, an unrelated ""Website Credentials"" field is showing. This is incorrect. - DONE
Unable to Save Website Details :- Users are unable to save the Website Details form — clicking Save has no effect or throws an error. This functionality needs to be fixed.",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,,
society website >> preview society website ( button) ,"review Society Website"" Button Not Working:- The ""Preview Society Website"" button is currently non-functional. When the user clicks on it, an error appears: 404 – This page could not be found. This indicates that the functionality has not been implemented yet

The ""Preview Society Website"" button should be disabled by default until the following conditions are met: Domain name is set , Home page banners are configured

When the button is disabled, display the following message in red font below the button:- ""Set domain name and home page banners to enable website""",,,FUNCTIONAL,OPEN,Bilal Momin,Partially Done,,,,
society website >> home,"Multiple Save Buttons Displayed:- the interface is showing more than one Save button, which is confusing and incorrect. Only a single Save button should be visible. - DONE

Save Button Not Working:- Clicking the Save button does not perform any action. this suggests the Save functionality has not been implemented yet and needs to be developed.",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,,
society website >> gallery,"Missing Multiple Photo Selection:- Currently, users can select only one photo at a time during upload. The system should support multiple photo selection at once to improve usability.

No Confirmation on Image Deletion:- When deleting an image, there is no confirmation popup. It should display a message:""Do you want to discard this image?""with two buttons: Yes and No before proceeding with deletion.

Upload Gallery Image Button Not Working:- The ""Upload Gallery Image"" button is currently non-functional. Clicking the button does not trigger any action or upload prompt, indicating the feature is not implemented or broken.

Static Badge and Image Type Selection Missing:- There should be an option to select the type of image (e.g., Interior, Exterior, Outdoor) while uploading. Once uploaded, a static badge indicating the selected type should be displayed on top of each image in the gallery.",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,,
society website >> developers ,"Missing Logo Upload Recommendations:- The Developer Logo field is missing important upload guidelines. Please add the following recommendation text near the upload button: ""Recommended formats: JPEG/JPG/PNG. Width should be greater than 100px. Height should align with text content length.""

Unable to Save Developer Information:- When attempting to save the developer information, an error appears: ""Failed to save developer information. Please try again."" This indicates a back-end or validation issue that needs to be resolved.",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,,
society website >> contributor ,"Unnecessary Website Field:- In both the Architect Logo and Facility Management Logo sections: The Website field is not required and should be removed or marked as optional.

Save Functionality Not Working:- The Save button in these sections is either: Not functioning, or Not implemented yet. Users are currently unable to save the uploaded information.",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,,
society website >> committee,"Committee Data Not Displaying After Save:- When saving committee data, the system displays a success message: ""Committee information saved successfully!""
However, the saved data is not appearing in the Committee section (under “Write about the committee of your society.”).",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,,
society website >> documents ,"Incorrect Flow for ""New Document"" :- When clicking ""New Document"", it incorrectly opens a popup to create a folder. Instead, it should provide an option to select or upload a document.

""Choose File"" Button Not Needed:- The ""Choose File"" button is unnecessary in this context and should be removed to simplify the interface.

Incorrect Submit Button Behavior:- The Submit button currently becomes enabled immediately after selecting a file, which is incorrect. It should only be enabled after: A new document is uploaded, or A new folder is created. Only then should clicking Submit save the new entry.

Unable to Save Documents or Folders:- after creating a new document or folder, the user is unable to save it — the operation does not complete successfully.",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,,
society website >> Amenities ,"Amenities Save Functionality Not Working:- When a user adds or selects new amenities and clicks save, the system displays a success message: ""Amenities saved successfully!"" However, the newly added or selected amenities are not actually saved. When the user returns to the Amenities tab, the previously added data is missing, indicating the save functionality is not working as expected.",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,,
society website >> website layout ,"not able to preview the logo file.
When the user returns to the website layout tab, the previously added data is missing, indicating the save functionality is not working as expected.",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,,
society website >> location map,"please check the screen shot "" comapre the map view in both old and new society """,https://prnt.sc/sACr0JNoW4Yr,https://prnt.sc/IpQwzT3t4T-l,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,,
society website  >> documents ,"Incorrect Implementation – Document Functionality Missing:- The current module is incorrectly using the same functionality as the Society Website section, instead of having a dedicated document management functionality
Please fix this and implement the correct and separate document functionality as intended for this section.",,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,,
society website  >>  allottees >> print / export ,"Missing PDF Download – Error Displayed:- When attempting to download PDF, the action fails and shows the following error message: ""Trying to access array offset on null"" This indicates a backend or data binding issue that needs to be resolved. (SURAJ SIR )

Export to Excel – Incorrect Member Details :- When using the Export to Excel feature, the file does not show proper member details. The exported data is either incomplete or incorrectly formatted (SURAJ SIR )",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,,
society website  >>  allottees >>approved >>transfer flat ,"page is not implemented yet shows error message ""404 Page Not Found ⚠️
We couldn't find the page you are looking for.",,,FUNCTIONAL,OPEN,"Bilal Momin, Kumail",Partially Done,,,,Api Pending from shweta side
society website  >>  allottees >>approved >> send invitaion,"page is not implemented yet shows error message ""404 Page Not Found ⚠️
We couldn't find the page you are looking for.",,,FUNCTIONAL,OPEN,"Bilal Momin,Kumail",Partially Done,,,,
society website  >>  allottees >>approved >> deactivate ,"Incorrect Flow on Member Deactivation:- When the user clicks the Deactivate icon: A success toast message is displayed. The deactivated member entries immediately disappear from the list. This is the incorrect flow.
Instead, when the user clicks the Deactivate icon, they should be redirected to the ""Revoke Allocation of Member"" page, where the user can proceed to revoke the unit for that member.",,,FUNCTIONAL,OPEN,"Bilal Momin,Kumail",Ready To Test,,,,
society website  >>  allottees >> do with selected ,"Missing Activation/Deactivation Controls:- ""Activate Selected"" and ""Deactivate Selected"" Buttons Missing . The options to bulk activate or deactivate members using ""Activate Selected"" and ""Deactivate Selected"" buttons are missing or not implemented.These controls are essential for managing multiple members efficiently.

Member Selection Checkbox Missing:- The checkbox to select individual or multiple members is also not present. This prevents users from performing bulk actions such as activation, deactivation, or allocation changes.",,,FUNCTIONAL,OPEN,Kumail Rizvi,Ready To Test,,,,
society website  >>  allottees >>approved >> tanent agreement ,"Missing Tenant Agreement Feature: -The Tenant Agreement feature is currently not implemented or missing from the system. This functionality is essential and should be available for managing tenant-related agreements.
For reference and implementation details, please check the screenshot from the old society module, where this feature is properly displayed.",,https://prnt.sc/msK7hJhQA96c,FUNCTIONAL,NEED TO DISCUSS,,NEED TO DISCUSS,,,,
society website  >>  allottees >>approved >> share certificate ,"Missing share certifiacte Feature: -The share certifiacte  feature is currently not implemented or missing from the system. This functionality is essential and should be available .
For reference and implementation details, please check the screenshot from the old society module, where this feature is properly displayed.",,https://prnt.sc/6CClyUKass6Y,FUNCTIONAL,OPEN,"Kumail Rizvi, Bilal",Partially Done,,,,
society website  >>  allottees >>approved >> unlink user ,"Missing ""Unlink User"" Icon in Action Section: -The ""Unlink User"" icon is currently missing from the Action section, or the functionality has not been implemented yet.
This icon is essential for allowing admins to unlink users from associated entities. Kindly fix this issue and ensure the feature is properly implemented and functional.",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,,
society website  >>  allottees >>approved >> view ,"Allottee Details Page – Incorrect and Incomplete Data:- Duplicate Data in Allottee Details and Change Log :The Change Log table is showing the same data as the Allottee Details section, which is incorrect.
Missing Fields :Several important fields are missing from the Allottee Details page, leading to incomplete information display.

Incorrect Data Display:- Both the Allottee Details and Change Log sections are showing incorrect or mismatched information. Please refer to the screenshot for a comparison between the old and new society views to correct and restore accurate functionality.",https://prnt.sc/3_MgsLVGW4dQ,https://prnt.sc/9c80haTjQ6nj,FUNCTIONAL,OPEN,Kumail Rizvi Bilal Momin,Ready To Test,,,,
society website  >>  allottees >>approved >> edit ,"Issues in Edit/Add Allottee Form
Member Type Field Not Auto-Reflected on Edit:- When editing an allottee, the Member Type field is empty, even if it was previously selected. The form should automatically populate the previously selected Member Type during edit. - DONE

Incorrect Member Type Dropdown Options:- The Member Type dropdown does not display the correct options. It should include: Primary, Associate, Nominal and Tenant .The current dropdown shows incorrect or incomplete values. - DONE

Unit Field Dropdown Not Working:- In the Unit field, the dropdown is not functioning, and the user is unable to select any value. 

Missing Mandatory Field Validation:- The form allows saving without selecting a Flat or Member Type, which are mandatory fields. Proper validation should be in place to prevent saving incomplete or invalid data. - DONE

The Edit/Add Allottee form should be split into two sections for better clarity: Personal Details, Unit Details - DONE",,,.,OPEN,Nishant Chorge Bilal Momin,Partially Done,,,,
society website  >>  allottees >>approved >>new  allottees ,"Unable to Add New Allottees – Selection Issues:- Users are unable to add new allottees because the following dropdown fields are non-functional:
Unit Number, Floor Number, Unit Category 
These fields are essential for allottee mapping, and their inaccessibility is blocking the add allottee process.

help note section is missing :- Help Note:
Member Type:
 Primary Member: The person who is the first person mentioned in the property registration agreement. This person is liable to pay the maintenance fee , property tax, parking charges and all the chargeable activities done within the society. - DONE

 Associate Member: The person who is allotted one or more units in the society in partnership with the Primary Member and is not the first person mentioned in the property registration agreement.

 Nominal Member: A occupant who is a family member of the Primary and/ or Associate member(s) or a person who is the care-taker on behalf of the Primary Member for a unit in the society. - DONE

 Tenant: A person who has taken the property on lease.

",,,FUNCTIONAL,OPEN,Bilal Momin Nishant Chorge,Partially Done,,,,
society website  >>  allottees >>unapproved,"Incorrect and Missing Action Buttons in Approval Section
Unnecessary Action Buttons in Approved Section: The Approved section is showing all action buttons, which is not required. Action buttons like Approve and Reject should not be visible once an item is already approved.

Missing Action Buttons in Unapproved Section:- In the Unapproved section, only Approve and Reject buttons should be visible. However, both these action buttons are currently missing or not implemented.",,,FUNCTIONAL,OPEN,Suraj Jamdade,NEED TO DISCUSS,Status we need to discuss with soiety team for filteration,,,
society website  >>  allottees >>primary member change request ,"Missing Primary Member Functionality:- The Primary Member functionality is currently missing or not implemented in the system.
Please refer to the screenshot from the old society module for how this functionality was handled.
The same Primary Member change request feature should be implemented in the current system.",,,FUNCTIONAL,OPEN,,,,,,
society website  >>  bulk allottees ,"Issues in Allottee Table Interface:- Allottee Type Dropdown Not Working ;The Allottee Type column is empty, and the dropdown shows no values.
It should include the following correct options:Primary, Associate , Nominal and Tenant - DONE


Add Row and Save Functionality Blocked:- Due to the missing Allottee Type options, users are unable to add a new row or save the allottee details, which blocks core functionality. - DONE

Continuous Loader Issue:- The table shows a continuous loading spinner, even when no action is being processed. This is incorrect behavior and should be fixed to reflect the actual loading state. - DONE

back button :- back button is missing from this page - DONE",,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,,
society website  >>  allottee type  ,"Allottee Page – Missing Features and Incorrect Implementation:- Help Note Missing :- The Help Note that guides users on how to use the page is currently absent. - DONE

Incorrect Page Display and Functionality:- The entire page functionality is either:Missing, Incorrectly implemented, or Not working as intended. Instead of showing Allottee Names, the page is currently displaying Category Names, which is incorrect behavior.

Mismatch Between Old and New Society Versions:- Please refer to the screenshots of the old and new society modules. The new version should match the structure and functionality of the old one, especially in how it displays and manages Allottee information.",https://prnt.sc/TMbhqCmHpwcf,https://prnt.sc/gQP9hdTU8aa5,FUNCTIONAL,OPEN,"Bilal Momin
Kumail Rizvi",Ready To Test,,,,
"society website  >>  Vehicle Registration
","edit and view link should be in blue color so it should look like a link 

Select by Filter Not Working:- The ""Select by Filter"" option is non-functional ; Users are unable to filter records, which impacts usability.- DONE",,,FUNCTIONAL,OPEN,Kumail Rizvi ,Ready To Test,,,,
society website  >>  Vehicle Registration>> export excel and pdf ,"Export Functionality Issues:- Export to Excel – shows incorrect data on export excel kindly match with old society excel data

PDF Download – Error Message:- Attempting to download PDF results in the following error: ""Trying to access array offset on null""",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,,
society website  >>  Vehicle Registration>> new / edit vechicle  registration ,"Vehicle Registration – Dropdown Selection Issue:- Users are unable to select Floor and Flat values from the respective dropdown fields. As a result, they are blocked from adding new vehicle registration entries.
This issue needs to be resolved to enable proper vehicle registration functionality.",,,FUNCTIONAL,OPEN,Nishant Chorge,PENDING,,,,
society website  >> parking allotment >> New / Edit Allocate Parking,"helpnote is missing 
Help Note: Parking can be alloted to Primary units(Flats,shops,etc).
Multiple Parkings can be alloted to a primary unit",,,FUNCTIONAL,OPEN,Bilal Momin ,Ready To Test,,,,
society website  >> parking allotment >> delete ,"this text  If revoked then, registration to 2 vehicles will be cancelled is missing from Impact this action could cause field 
",,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,,
society website  >> parking allotment >>export excel and pdf ,"Export Functionality Issues:- Export to Excel – shows incorrect data on export excel kindly match with old society excel data
total amount row is missing 

PDF Download – Error Message:- Attempting to download PDF results in the following error: ""Trying to access array offset on null""",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,,
"society website  >> Management Committee
","Committee Form – Save and Validation Issues
 Unable to Save Committee Form for “Provisional” or “Elected” Types: -When the user selects ""Provisional"" or ""Elected"" as the Committee Type, the form fails to save.
It incorrectly displays the error message: ""Staff Category Name is required."" This message is not relevant for these committee types and should either be removed or replaced with an appropriate validation message.

Validation for Minimum Members
If the user selects fewer than 5 members, the system has to shows the validation:""There should be at least 5 members in the committee.""",,,FUNCTIONAL,OPEN,Bilal Momin Kumail Rizvi Nishant Chorge,Ready To Test,,,,
Society website >>NOC Forms template ,"NOC Form View Discrepancy:- Please refer to the screenshot and compare the NOC form view in both the old society and new society modules.
The current implementation in the new society version appears to be incomplete or inconsistent with the older version and should be aligned accordingly.

Download NOC Form Not Working:- When attempting to download the NOC form, the system displays the following error message: ""Failed to download NOC Passport""",https://prnt.sc/_U0CBy1ISiEl,https://prnt.sc/ZLyfAOJ38pam,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,,
Society website >>NOC Forms,"Auto-Fill Fields Not Working:- The following fields in the NOC form should be auto-populated but are currently not:
Letter Date – should auto-fill with today’s date
Society Name – should auto-select based on the logged-in user's society
Since Staying Date – should auto-fill with the member's move-in date
These fields are essential and should not require manual entry.

Preview NOC Button Not Working:- Clicking the ""Preview NOC"" button results in an error: ""Cannot read properties of undefined (reading 'message')"" - SURAJ SIR

Error on NOC Form View After Save:- After successfully saving the NOC form (confirmed by the success toast message), the system redirects to the NOC Form view.However, it shows an error: ""Error fetching data""This means the data is either not being saved properly or not retrieved correctly, despite the success message.Please refer to the screenshot for a comparison between the working view in the old society module and the broken behavior in the new module. - DONE",https://prnt.sc/TJ0RTW5o_7NT,,FUNCTIONAL,OPEN,"Kumail Rizvi,BIlal",Ready To Test,,,,
Society website >>Notice and circulars ,"Reference Number Column Should Be Clickable:- The Reference Number column should act as a clickable link, allowing users to quickly access the detailed view of the respective NOC form. - DONE
View Action Not Working 

Under the Action menu, clicking ""View"" triggers the following error: 404 – Page Not Found ⚠️""We couldn't find the page you are looking for."" This indicates a broken route or missing page implementation, and needs to be resolved.

Incorrect Action Label and Missing Print Option:- The system currently shows ""View Responses"" under actions, which is incorrect in this context.Instead, there should be a ""Print"" action button to allow users to print the NOC form directly.",,,FUNCTIONAL,OPEN,Kumail Rizvi Bilal,Partially Done,,,,
Society website >>Notice and circulars >>new notice ,"Member Field – Dropdown Missing & Validation Issue:- The Member field is currently not displaying the dropdown list, making it impossible to select a member.
Additionally, this field should be marked as mandatory, but the form allows submission attempts without it. - DONE

Unable to Save ""Add New Notices & Circulars"":- When trying to add a new notice or circular, the Save functionality is not working. The system does not save the data, and no confirmation or action is completed, indicating a broken or unimplemented feature.",,,FUNCTIONAL,OPEN,Kumail Rizvi Nishant Chorge Bilal Momin,Ready To Test,,,,
Society website >>Notice & Circular Templates,please check the screenshots,"https://prnt.sc/i5TsrcveJa7q

https://prnt.sc/BmzD0_O89f2s",,FUNCTIONAL,OPEN,"Bilal Momin, Kumail",Ready To Test,,,,
"Society website >>Non Member Master
",please check the screenshots,https://prnt.sc/bXdG3M1XNb41,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,,
"Society website >>Non Member Master
>> add non member "," Non-Member Bill – Fields Should Not Be Mandatory
For non-member billing: Fields like GSTIN Number, PAN Number, GST State Code, and HSN/SAC should not be mandatory. Currently, the system treats them as required, which is incorrect. - DONE

Non-Member Bill – Missing Auto-Population on Edit :- When editing a previously saved non-member bill: The fields do not auto-fill with the saved data.
This is incorrect – the system should populate the form with the existing values.",,,FUNCTIONAL,OPEN,"Kumail Rizvi,Bilal ",Ready To Test,,,,
Society website >>inventory ,"Missing “Add Assets & Inventory Settings” in New Society:- For newly created societies, if the user has not set up the “Add Assets & Inventory Settings”, they are unable to make any investment.This setting functionality is completely missing in the new society setup.
Please refer to the screenshots of the old society, where this setting is available and functional. Implement the same “Add Assets & Inventory Settings” functionality in the new society setup to ensure feature parity.",https://prnt.sc/d54b2MSEV8DH,"https://prnt.sc/q35dUjKqzKaA

https://prnt.sc/Mk9CuRTlfrea",FUNCTIONAL,OPEN,Neeraj Sharma,NEED TO DISCUSS,,,,
REPORTS,,,,,,,,,,,
reports >> TDS  Receivable,"date correct - print / excel / pdf pending - in pdf society address is missing 
and excel - shows extra column ""RECP03584"" which is incorrect ; also show summary incorrectly in unit and member name column 
filter is not working ",,,FUNCTIONAL,OPEN,"BILAL, Kumail",Partially Done,,,VERIFIED,
reports >> TDS  payable ,"Date Filters – Default Selection Needed: On the TDS Receivable page: The ""From Date"" and ""To Date"" of current month  filters should be auto-selected by default. - DONE

Summary Table Alignment:- In the Summary section: Labels like ""TDS Deducted"" and ""Payment Amount"" and their values should be left-aligned.
rename tds payable label to summary -  DONE

Missing Functionality – PDF and Print:- The PDF download is not working.shows error message ""Trying to access array offset on null"" . The ""Print Report"" button or functionality is completely missing. in pdf provide left to right scroll as user is not able to see all columns

Excel Report – download excel report is not working shows error message ""The route api/admin/reports/tdsPayable/download/download/excel could not be found."". it shows the half records 10 only in old society is shows 32 records for same report ; also column name GST AMOUNT , TOTAL AMOUNT AND TDS  rate are missing . ALSO showing taxable anount and total amount wrong in excel.

Missing Report Header Section:- In the TDS Receivable report: The section showing the Society Name, Report Name, and Selected Date Range is missing or not implemented at the top of the table. It should display the following information clearly above the report table: DONE

Society Name: SHWETA CO OP HOUSING SOCIETY LTD 
Report Name: TDS payable
Date Range: 01/06/2025 to 30/06/2025

Sum is missing :- sum of TDS amount and payable amount is missing in TDS payable table ( for refrence please check old society ) - DONE",https://ibb.co/chWFZH4z,,FUNCTIONAL,FUNCTIONAL,Kumail Rizvi Suraj Jamdade,Partially Done,,,,
reports >> Members Receivable," Issues with “Select By” and “Filter By” Functionality:- in the ""Select By"" field: Options like ""Maintenance Due smaller then equal to "" and ""Incidental Due smaller then equal to "" are missing. - DONE

For the ""Due On Date"" filter: The calendar icon is missing, so the user cannot select a date. ""Select By"" and ""Filter By"" functionalities are not working at all.

Missing Header Information:- The header section should show: Society Name, Report Name Selected ""Search By"" and ""Filter By"" values Currently, none of these are displayed. - DONE

UI Spacing Issue:- There is no space between the ""Select Date Range"" section and the table header. - DONE

Summary Table Issues:- Alignment: Labels like Maintenance Dues, Ledger Balance, and Incidental Dues, along with their values, should be left-aligned. - DONE

Calculation Error:- The totals for Maintenance Dues, Ledger Balance, and Incidental Dues are incorrect. They do not match the values from the old society reports and need verification. - WIP

Missing Functionality – PDF and Print:- The PDF download is not working.shows error message ""Method App\Console\Commands\Workflows\Reports\MembersReceivableReportDownloadWorkflow::formatNumberForExcel does not exist."" show society address also and if pdf ahave multiple pages show society name and address on each pdf page 

Excel Report – download excel report is not working shows error message ""Method App\Console\Commands\Workflows\Reports\MembersReceivableReportDownloadWorkflow::formatNumberForExcel does not exist. show column empty which has value ""0"" show 0 instead showing empty
""

filters by :- now user is able to select multiple filter whoch is incorrect user only required to select on filter by at a time 
incidental due count is incorrect ( bhoomi harmony society ) 

",,,FUNCTIONAL,OPEN,"Suraj Jamdade
Kumail Rizvi Bilal Momin",Partially Done,,,,Pending from Bilal Momin side
reports >> Members Incidental Receivable,"Date Filters – Default Selection Needed: On the Members Incidental Receivable page: The ""due on date ""   search field with todays date should be auto-selected by default. - DONE

Summary Table Alignment:- In the Summary section: all labels  values should be left-aligned. now it shows incorrect lable kindly comapre it with old society and fix them 
rename Members Incidental Receivable to summary - DONE

Missing Functionality – PDF ,Print - summery section is missing ; report comes incorrect
and  excel :- shows blank instead of 0 for the coloumn which has 0 value in excel export 
when user click first time on this report it shows error message "" Trying to access array offset on null"""" on second time it exported perfectly
Missing Report Header Section:- society anme and report name is missing from header. - DONE 

filters :- filters are not working DONE

Members Incidental Receivable table column  lables  :- all coulmn name lables are incorrect please verify with old society 

Late Payment Charges Summary comes incorrect 
Shifting Charges Summary amount comes incorrect
Intercom Instrument Purchase Summary comes incorrect",,,FUNCTIONAL,OPEN,"Suraj Jamdade
Kumail Rizvi",Partially Done,,"Move In - 

old society report (0 which is wrong)
https://ibb.co/ynJvDckq

new society report : (3000 whcih is right)
https://ibb.co/dwhVm9qJ

Actual data : 
https://ibb.co/1Bw87Nq



Late payment charges, Intercom_instrument_purchase_summary and 
Shifting charges, fixed.",,"Bilal Momin

Please check the UI related issues."
reports >>Member Invoice Detail,"Incorrect Filter Section Behavior:- In the ""Select By"" dropdown: It incorrectly combines all filter options (like ""Unit Creation Date"", ""Unit Name"", and ""Order"" with values like Ascending/Descending) into a single dropdown, which is not correct. Each of these should be shown as separate dropdowns or filters.

Missing Header Section:- The report header section is missing completely.
It should clearly display:
SHWETA CO OP HOUSING SOCIETY LTD  
Member Invoice Detail  
Invoice Date: 17/06/2025  
Sort By: Unit Creation Date  |  Order: Ascending DONE

Summary Table Issues – Alignment In the Summary section: Labels like Total Invoice, Invoice Period, and Total Payable should be left-aligned. - DONE

Missing Labels in Report:- The following important labels are missing or not working in the Member Invoice Detail Report: Education and Training Fund, Non-Occupancy Charges , Parking - 2 Wheeler , Parking - 4 Wheeler, Repair Fund, Sinking Fund, Credit/Adjustment, Net Due, Advance/Credit These are not showing or not functioning, which causes incomplete report data. - DONE (added labels, made it same as old soc, FTPL)

Incorrect Data Display:- The data shown in the report is incorrect. This includes misaligned values, mismatched totals, or data not corresponding to actual invoices. It needs a thorough comparison and fix against reliable source data. - DONE

Missing Functionality – PDF and Print:- The PDF download is not working.shows error message ""Trying to access array offset on null"" . The ""Print Report"" button or functionality is completely missing.

Excel Report – data comes in column is mismatched and also total row  of this report is missing ; now shows this error message on report ""round(): Argument #1 ($num) must be of type int|float, string given""

filter >> creation >> descending - shows advance credit wrong 
also shows periods column extra 
filter by should be single select now user is able to select multiple which is incorrect
total payable amount in summary comes incorrect (  cyber one society )",,,FUNCTIONAL,OPEN,Kumail Rizvi,Partially Done,,,,
reports >>Member Incidental Invoice Detail,"Horizontal Scrolling Issue – Table Cutoff :- The report table cannot be scrolled horizontally, which leads to a major visibility problem. As a result, important columns/labels are not visible on the screen, including:
Parking - 4 Wheeler, Parking - 2 Wheeler, PMC Property Tax – Common Area, PMC Property Tax – Stilt Parking, Interest, Tax, Invoice Amount, Credit/Adjustment, P Arrears, I Arrears, Net Due This affects data accessibility
 
 Summary Table Issues – Alignment In the Summary section: Labels like Total Invoice, date  and invoice amount  should be left-aligned. shows total incorrect in sumamry please check with old society  - DONE

Missing Header Section:- The report header section is missing completely. - DONE
It should clearly display:
SHWETA CO OP HOUSING SOCIETY LTD  
Member Invoice Detail  
Invoice Date: 17/06/2025  

Date fields– Default Selection Needed: On member incidental invoice page: The ""From Date"" and ""To Date"" of current month  filters should be auto-selected by default. - DONE

Broken Export Options – PDF, Print, Excel:- When the user clicks PDF, Print  :- shows error message :Method App\Http\Controllers\IncomeDetailsController::incidentalInvoiceDetailReportDownload does not exist.
 or Excel: It shows a ""404 – This page could not be found"" error. this means export/download functionality is completely broken.
-rename column name ""member ""to""bill to "" 
- payment reversal correction column is not required 
- p Arrears values are incorrect 
- net  due value are incorrect  
- invoice number value is incorrect 
- total number rows on filter comes incorrect 

total payable and total invoice comes incorrect ",,,FUNCTIONAL,OPEN,"BILAL
Kumail Rizvi",Partially Done,,"Old society 
https://ibb.co/PsnxmGX2

Move in amount should be added in invoice amount and net due amount and hence net due amounts summed up should be total payable.
As per account team's Sonal.

New society 
https://ibb.co/TDQzg24q

Same case with other amounts, hence total payable amount is more than the old society.",,
reports >>Member Unit Statement ,"Unit Field Error – Dropdown Fails to Load:- The Unit field in the report section shows the following error message: ""Failed to load option"" Because of this, the user is unable to test or use the report functionality, blocking further actions. - DONE

Summary Table – Alignment Issues:- In the Summary section: Labels like:Total Debit, Total Credit , Total (Debit/Credit) Should be left-aligned for consistent readability. Currently, the alignment appears broken or inconsistent. Rename Member Unit Statement Report"" should be renamed to:""Summary""

- on filters - shows total records entry are incorrect  
- all filter is missing ( download filter for all / functaionality is missing ) 
- for flat 02 - tharwani residency ( it shows error ; error fetching data)  
- print / excel :- not showing full data of report also sumamry section is missing 
- pdf :-not showing full data of report also sumamry section labels  is missing 

now on pdf and excel dowanload shows error message "" network error ""
",https://ibb.co/BVTbFq0n,,FUNCTIONAL,OPEN,"BILAL
Kumail Rizvi",Ready To Test,,,,
reports >>Member Unit Ledger Statement,"Unit Field Error – Dropdown Fails to Load:- The Unit field in the report section shows the following error message: ""Failed to load option"" Because of this, the user is unable to test or use the report functionality, blocking further actions.

Summary Table – Alignment Issues:- In the Summary section: Labels like:Total Debit, Total Credit , Total (Debit/Credit) Should be left-aligned for consistent readability. Currently, the alignment appears broken or inconsistent. Rename Member receipt  Report"" should be renamed to:""Summary""
- Total Debit, Total Credit , Total (Debit/Credit)  summary incorrect 
- select all filters are missing 

name incorrect shows ""Member Receipt ReportSummary"" instead of ""Member Unit Ledger Statement""
shows id column which is not required 
not showing the opening balance 
in summary shows Total (Debit-Credit) incorrect 
- print / excel / PDF  fucntioanlity is not working

on applying filer - shows error message error fetching data",https://ibb.co/5hg4nKKw,,FUNCTIONAL,OPEN,"BILAL
Kumail Rizvi",Ready To Test,,,,
"report >>Nonmembers Receivable
","""Date Filters – Default Selection Needed: On the non Members Receivable page: The """"due on date """"   search field with todays date should be auto-selected by default.
in select by dropdown there is a no option for ""all""

Summary Table Alignment:- In the Summary section: all labels  values should be left-aligned.rename Members Incidental Receivable to summary

Missing Functionality – PDF ,Print :- shows error message "" Trying to access array offset on null"" 
 and  excel :-these funcationality are not working for this report section  as shows no data on export excel 

Missing Report Header Section:- society name and report name, due as on and filter by  is missing from header. 

on applying filter it shows error message error fetching data ",,,FUNCTIONAL,OPEN,"BILAL
Kumail Rizvi",Ready To Test,,,,
report >>income receipt report ," Date Filters – Default to Current Month:- On the Income Receipt Report page: The ""From Date"" and ""To Date"" filters should be auto-filled with the current month's date range. Currently, the user must select them manually, which is inefficient. - DONE

Missing & Non-Functional Receipt Type Filter :- The ""Receipt Type"" filter is: Missing from the page UI. And if present (manually enabled via dev tools or backend), it does not function properly. Search functionality is also not working, even when filters are applied.

Missing Calendar Icon for “Select by Payment Date”:- When selecting ""Select by Payment Date"": The calendar icon for date selection is missing. Users cannot choose a date without it, making the filter non-functional. It must show a working calendar icon for proper usability.

header section is missing :- SHWETA CO OP HOUSING SOCIETY LTD
Member Receipts
Payment Mode : All , From Date : 2025-06-01 , To Date : 2025-06-30 ( it should change everytime based on filter applied ) DONE

lable missing :- receipt date coulmn is missing - DONE

Summary Table Alignment:- In the Summary section: all labels  values should be left-aligned. rename Members ""Member Receipts Report"" to summary - DONE

Functionality not working  –Missing Functionality – PDF ,Print :- shows error message "" Trying to access array offset on null"" 
for EXCEL :- IT DOES NO shows the applied filter like payment mode: all and from date 2025-07-01 to 2025-07-31


search by payment mode and search by unit number , invoice number , unit number , apid by , cheque number, payment date  and all filter are missing ",,,FUNCTIONAL,OPEN,Kumail Rizvi Bilal Momin,Partially Done,,,,
report >>expesne report ,"check screenshot :- plese check the screenshot and compare the data of old and new society  - DONE

Functionality not working – PDF ,Print and excel :-these funcationality are not working for this report section shows error messageTheMethod App\Http\Controllers\ReportsController::downloadExpenseReport does not exist. DONE

header section is missing :- SHWETA CO OP HOUSING SOCIETY LTD - DONE
expesne report
From Date : >= 01/06/2025 , To Date : <= 30/06/2025 ( it should change everytime based on filter applied

PDF  :- shows error message ""PDF URL is missing in the response"" ; this pdf functaionality is not required 
excel :- from date and to date filter is missing ; not showing the full data of column ""perticular expense head ""

", ,https://prnt.sc/-vvlTO0NsSut,FUNCTIONAL,OPEN,"Bilal Momin
Kumail Rizvi Suraj Jamdade",Ready To Test,,,,
report >>Expense Payment Report,"pdf - report name , society address and from date to date filter is missing in pdf 
excel :- shows write off column is blank instaed of showing it 0 . also the filter from date to date is missing ",,,FUNCTIONAL,OPEN,Kumail Rizvi,Partially Done,,,,
report >>Expense budget Report," Financial  year Filters – Default to Current Financial year :- On the expense budget Report page: The Financial  year  filters should be auto-filled with the current year date range. Currently, the user must select them manually, which is inefficient. - DONE

header section is missing :- SHWETA CO OP HOUSING SOCIETY LTD
expesne budget report 
Financial Year : 2021-2022 ( it should change everytime based on filter applied DONE

Functionality not working :-  PDF ,Print  :-these funcationality are not working for this report section shows error message :- Trying to access array offset on null"" 
 and  excel :- applied filter is missing in excel report like financial year 2019-2020 also the total count is incorrect .",,,FUNCTIONAL,OPEN,Kumail Rizvi,Partially Done,,,,
report >> GST Report,"Date Filters – Default Selection Needed: On the GST report page: The ""From Date"" and ""To Date"" of current month  filters should be auto-selected by default. - DONE

check screenshot :- plese check the screenshot and compare the data of old and new society - DONE

excel export :- excel export functioanlity is not implemented on this page 

outward and inward:-  filter is not working  - DONE

header section is missing :- SHWETA CO OP HOUSING SOCIETY LTD
GST  report 
From Date : = 01/06/2025 , To Date : = 30/06/2025 ( it should change everytime based on filter applied ) DONE

column missing SGST (9.000%)        CGST (9.000%)

Functionality not working :-  PDF ,Print  :-these funcationality are not required for this report 
 and  excel :- shwos error message "" The route api/admin/accounts/gstReports/excel could not be found.""
amount and total is missing in this excel report ",https://prnt.sc/NiKE6_zf2FDF,https://prnt.sc/BGamrO7yTip-,FUNCTIONAL,OPEN,Kumail Rizvi,Partially Done,,https://ibb.co/FbksT1Mk,,
report >> voucher Report,"excel :- shows data correct only showing 1 extra column of sr .no which is not required 
and pdf :- shows blank data in some columns",,,FUNCTIONAL,OPEN,"BILAL
Kumail Rizvi Suraj Jamdade",Ready To Test,,,,
report >> receipt Report,"Dropdown Filters – Incomplete and Misaligned:- In the ""Select By"" filter section: Dropdowns for Status, Type, Mode, and Unit are not showing all values. The layout/positioning of these filters is also incorrect and misaligned, affecting usability. - DONE

Date Filters – Default to Current Month:- On the Receipt Report page: The ""From Date"" and ""To Date"" fields should automatically populate with the current month’s range. Currently, the user has to manually select the dates, which is inefficient. - DONE

Filter Functionality Not Working:- The ""Filter by Type"" dropdown:Is present but not functional. Selecting any filter option does not update or reload the report data, making it ineffective. - DONE

Missing Summary Section Table:- The summary table that should provide a quick overview of totals or key figures is completely missing from this report. This makes it hard to get a consolidated view of the receipt data. - DONE

Missing Column Values:- The following columns in the report show no values even when data exists:Receipt From, Payment Reference, Building / Unit, Write Off and Status - DONE

Broken Export Features – PDF, Print, Excel:- When the user clicks PDF, Print, or Excel download: It fails with the following error: ""Method App\Http\Controllers\ReportsController::downloadExpenseReport does not exist.""

missing header section :- SHWETA CO OP HOUSING SOCIETY LTD
Receipt Report
Filter : Unit - All, From date - 01/06/2025, To date - 30/06/2025, Status - All, Type - All, Mode - All ( it should change everytime based on filter applied ) - DONE
excel :- paid by column data is missing , payment reference  column name is missing  ; NOT SHOWING full data in this report also paid amount comes incorrect on excel
and pdf :- shows blank data in some columns ; NOT SHOWING full data in this report also paid amount comes incorrect on PDF  ( not able to download excel and pdf shows error message ""network error""

Total amount comes incorrect in this report  - kumail check this ( bhakti park society)",,/home/<USER>/Downloads/Income Receipt Report (1).xlsx,FUNCTIONAL,OPEN,Kumail Rizvi,Ready To Test,,,,"Nishant Chorge

Need to discuss on this point."
report >>Bank Reco Statement,"
 Missing Filters – Bank and Type:- The ""Select Bank"" and ""Type"" filters are missing from the page. These are essential for narrowing down records and must be added to the filter section. - DONE

Date Filters – Should Default to Current Month:- On the Bank Reco Statement page: The ""From Date"" and ""To Date"" fields should be auto-filled with the current month’s range. Users are currently required to enter these manually, which slows down report access. - DONE

Missing Header Section:- The header section with report details is not displayed. It should dynamically reflect the applied filters and appear like this:
SHWETA CO OP HOUSING SOCIETY LTD  - DONE
Bank Reco Statement
Date : 01/01/2025 To 30/06/2025 , Bank : STATE BANK OF INDIA-*********** , Filter By : Bank Date , Type : All (it should change everytime filter is applied )pgsql

Filter By: Bank Date  Type: All This header should update automatically based on the filters selected. - DONE

Missing Column Values :- Even when data is available, the ""Bank Date"" column shows no values in the table. - DONE 

PDF AND PRINT  :- paid by column data is missing , payment reference  column name is missing  , not showing 0 in write off column - need left to right scroll to view all column in  PDF, particulars and narration column comes blank which is incorrect ; total withdrwal amount comes incorrect in PDF 
EXCEL :-  paid by column data is missing , payment reference  column name is missing  AND show total amount below withdrawal coloumn instead of date column ; particulars and narration column comes blank which is incorrect ; total withdrwal amount comes incorrect in EXCEL , also show withdrwal amount in deposite column and dposite amount in withdrwal column which is incorrect
Total amount comes incorrect in this report  - kumail check this ( bhakti park society)

not able to check these points as it shows error message ""Error fetching data""",,,FUNCTIONAL,OPEN,Kumail Rizvi,Partially Done,,"Old Society : 
https://ibb.co/Dft0Yz8j

New Society :
https://ibb.co/Y75HQpm6",,"Missing Filters – Bank and Type:- The ""Select Bank"" and ""Type"" filters are missing from the page. These are essential for narrowing down records and must be added to the filter section.


The above filters are present in the page. They are not seggregated, they are combined."
Reports>>Member>>Member Contact Detail," Email Address and Mobile Number Filters – Should Be Separate :- Currently, Email Address and Mobile Number are either: Combined into a single filter or Not clearly separated in the filter section.
These two fields should be provided as independent filters, allowing users to: Search by Email Address only Or by Mobile Number only, as needed

 header section is missing :- SHWETA CO OP HOUSING SOCIETY LTD
Member Contact Detail
Filter : Email - All, Mobile - With ( it should change every time filter is applied ) DONE

Functionality not working :-  PDF ,Print and  excel :-these funcationality are not working for this report section  shows success message on download of both pdf and excel but download nothing ",,,FUNCTIONAL,OPEN,Kumail Rizvi,Partially Done,,,,
Reports>>Member>>Member signature ," Email Address and Mobile Number Filters – Should Be Separate :- Currently, Email Address and Mobile Number are either: Combined into a single filter or Not clearly separated in the filter section.
These two fields should be provided as independent filters, allowing users to: Search by Email Address only Or by Mobile Number only, as needed

 header section is missing :- SHWETA CO OP HOUSING SOCIETY LTD
Member Contact Detail
Filter : Email - All, Mobile - With ( it should change every time filter is applied ) DONE

Functionality not working :-  PDF ,Print and  excel :-these funcationality are not working for this report section  shows error message ""404 page not found """,,,FUNCTIONAL,OPEN,Kumail Rizvi,Partially Done,,,,
Reports>>Member>>tanent signature ,"Email Address and Mobile Number Filters – Should Be Separate :- Currently, Email Address and Mobile Number are either: Combined into a single filter or Not clearly separated in the filter section.
These two fields should be provided as independent filters, allowing users to: Search by Email Address only Or by Mobile Number only, as needed

 header section is missing :- SHWETA CO OP HOUSING SOCIETY LTD
Tenants Signature List
Filter : Email - All, Mobile - All ( it should change every time filter is applied ) DONE

Functionality not working :-  PDF ,Print and  excel :-these funcationality are not working for this report section  shows error message ""404 page not found """,,,FUNCTIONAL,OPEN,Kumail Rizvi,Partially Done,,,,
Reports>>credit accounts >>member advances ,"Missing Sum in Member Advance Table :- The total sum row at the bottom of the Member Advance table is missing. This summary row is critical for giving users a complete overview of balances. - DONE

Summary Table Alignment:- In the Summary section: all labels Refundable Balance and Adjustable Balance  values should be left-aligned. now it shows incorrect lable kindly comapre it with old society and fix them 
rename Members advance  to summary - DONE

Broken Export Options – PDF, Print, Excel:- PDF, Print, and Excel functions are not working for this report shows error message ""The route api/credit-accounts/memberCreditNote/download/pdf could not be found.

missing header :- SHWETA CO OP HOUSING SOCIETY LTD
Member Advance - DONE",,,FUNCTIONAL,OPEN,Kumail Rizvi,Partially Done,,,,
Reports>>credit accounts >>non member advances,"Incorrect Data in Non-Member Advance Report:- The Non-Member Advance Report is currently displaying the same data as the Member Advance Report, which is incorrect.

Missing Functionality – The functional logic for Non-Member Advance is not implemented. The system should: Fetch and display only non-member advance data

Broken Export Options – PDF, Print, Excel:- PDF, Print, and Excel functions are not working for this report shows error message ""The route api/credit-accounts/memberCreditNote/download/pdf could not be found.

shows error message error fetching data ""Cannot read properties of undefined (reading '0')""",,,FUNCTIONAL,OPEN,"Nishant Chorge
Kumail Rizvi",Partially Done,,,,"Nishant Chorge

Pass correct parameter"
Reports>>parking >> Parking Allottment Detail,"Parking Allotment Details Report – Functionality Not Implemented:- The Parking Allotment Details Report is currently non-functional. It displays the error message: ""Error fetching data""
please chekc the screenshot - DONE

Functionality not working :-  PDF ,Print ;- shows parking for column is empty in PDFCannot read properties of undefined (reading '0')
show parking for column is empty in excel",https://prnt.sc/3wrQltSOo99Z,,FUNCTIONAL,OPEN,Kumail,Ready To Test,,,,
Reports>>parking >> Vehicle Registration Detail,Reports>>parking >> Vehicle Registration Detail,,,FUNCTIONAL,OPEN,"Kumail
Suraj Jamdade
Bilal Momin",Partially Done,,,,"Have checked, shows 9 entries correctly, in Shweta Co Op HS.
"
reports >>parking >>Registered Vehicle Count Detail,"Missing Header Section:- The report does not display a header with the necessary context. It should dynamically show:
SHWETA CO OP HOUSING SOCIETY LTD - DONE
Registered Vehicle Count Detail
Filter : All ( it should changes evertime filter is applied ) 

Data Mismatch – Old vs New Society:- There's a discrepancy in data between the old society system and the new one: Old society report shows""Flat/01"" data  open parking 9 , 2wheeler (Owner|Tenant) 6|0, 4wheeler (Owner|Tenant) 3/0  .
New society report  shows Flat/01"" data  open parking 3 , 2wheeler (Owner|Tenant) 2|0, 4wheeler (Owner|Tenant) 1/0  .This mismatch should be corrected. - SURAJ SIR

Broken Export Options – PDF, Print, Excel:- PDF, Print, and Excel functions are not working for this report shows error message ""The route api/credit-accounts/memberCreditNote/download/pdf could not be found.
EXCEL - SHOWS no data in shaded parking which is incorrect also not showing the total of shaded parking ; open parking count is incorrect 
",,,FUNCTIONAL,OPEN,"Suraj Jamdade
Kumail Rizvi,",Partially Done,,,,
reports >>helpdesk >>complaints,"missing header section :- SHWETA CO OP HOUSING SOCIETY LTD
Complaints DONE

Broken Export Options – PDF, Print, Excel:- PDF, Print, and Excel functions are not working for this report. Trying to export shows the error message: ""404 Page Not Found""

Improper Filter Positioning:- The ""Filter By"" dropdown is not placed correctly in the UI. It should appear after the ""Date Range"" filter, but currently it is misaligned or out of order, affecting usability.

Cannot Remove Date Range Filter:- Once a date range is selected: The user is unable to clear or remove it. There should be an option to reset or deselect the date range to restore full data visibility. - DONE

Incorrect Filter Options – Overdue & Escalated:- The dropdown includes ""Overdue"" and ""Escalated"" as filter options.These options are Incorrectly labeled.",,,FUNCTIONAL,OPEN,"Nishant Chorge
Kumail Rizvi, BIlal ",Partially Done,,,,
reports >> statutory >>J registor ,"missing header section :- SHWETA CO OP HOUSING SOCIETY LTD
FORM-J - DONE

action - view :- this functionality is not working shows error message ""404Page Not Found ⚠️
We couldn't find the page you are looking for.""

Broken Export Options – PDF, Print, :- not working shows error message :- Trying to access array offset on null
Excel:- PDF, Print, and Excel functions are not working for this report. Trying to export shows the error message: ""404 Page Not Found""
shows ID incorrect in PDF ",,,FUNCTIONAL,OPEN,"Kumail
Suraj Jamdade
Nishant Chorge",Partially Done,,,,Page title and View Pge is pending to be built.
SETTING,,,,,,,,,,,
Setting >> access control >> users,"Manage Role Popup Not Loading:- The ""Manage Role"" popup fails to load when triggered.please check the screenshot ",https://prnt.sc/GgkUYdCHbmwd,,FUNCTIONAL,OPEN,Nishant Chorge,Ready To Test,,,VERIFIED,
Setting >> access control >> users roles >> edit,"Cannot Select Inactive Status from Dropdown:- The ""Inactive"" option in the dropdown is not selectable. This issue is shown in the attached screenshot.
Expected behavior: User should be able to select ""Inactive"" from the dropdown. DONE

",https://prnt.sc/DlcIgtpvJziB,,FUNCTIONAL,OPEN,Bilal Momin Nishant Chorge,Ready To Test,,,VERIFIED,
setting >> staff ," PDF Export Not Working:- The PDF generation or download feature is currently not functioning. This issue needs to be resolved to allow users to export reports as PDFs. - DONE

Capture Image from Camera Not Working (Edit/Add Staff):- While trying to add or edit staff: The ""Capture Image by Camera"" feature is not working. This can be verified through the attached screenshot.
Expected behavior: Users should be able to capture and upload staff images via camera. - DONE

View Staff – Attachment Not Clickable :- In the ""View Staff"" section: Uploaded attachments are not clickable. Users should be able to click and view attached documents or images. - DONE

Add New Staff – Save Not Working:- While trying to add a new staff member: The ""Save"" button does not function, and the staff entry is not saved.",https://prnt.sc/bXbAYC89e601,,FUNCTIONAL,OPEN,Nishant Chorge Kumail Rizvi Bilal Momin,Ready To Test,,,VERIFIED,
setting >> edit staff ,"on edit Language Spoken, RFID / Biometric ID and  Note field is not reflecting ",,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,VERIFIED,
setting >> staff >> staff category ," Unnecessary Error Message When Saving Without Edits:- When the user edits a staff category but makes no changes and clicks Save:An error message appears. please check the screenshot
.Expected behavior: Show a green toast message like: No changes made” (success type), instead of showing an error.

Placeholder Text Needs Renaming:- The placeholder staff_type_names-0 should be renamed to:“Enter Category Name”
 Label Text Needs Renaming:-The label New Category Name-1 * should be renamed to:-“New Category Name”",https://prnt.sc/kixEKeFUEZNs,,FUNCTIONAL,OPEN,Nishant Chorge,Ready To Test,,,,
setting >> complex setting >> buildings / delete buildings , PDF Export Not Working:- The PDF generation or download feature is currently not functioning. This issue needs to be resolved to allow users to export reports as PDFs.,,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,VERIFIED,
setting >> complex setting >> unit categories ," PDF Export Not Working:- The PDF generation or download feature is currently not functioning. This issue needs to be resolved to allow users to export reports as PDFs.

Total Row Missing in Downloaded Excel:- In the downloaded Excel report, the ""Total"" row is missing entirely. The report should include a Total row at the bottom summarizing key columns.Please compare with the old society’s Excel report for correct formatting and implementation.",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,VERIFIED,
setting >> complex setting >> new unit categories ,"Incorrect Help Note Content:- the help note content in the new society is incorrect. Please refer to the attached screenshot showing a comparison between the old society and new society versions.
and Update the help note content in the new society to match the correct version from the old society.

shows duplicate labels :- please check the screen shot 
","https://prnt.sc/30jWej3suE10

https://prnt.sc/UgcydT_t9SIy",https://prnt.sc/SR9aX9KAY0jQ,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,VERIFIED,
setting >> complex setting >> complex unit  >> new unit ,"
Duplicate Labels Displayed :- The UI is showing duplicate field labels.This can be seen in the attached screenshot.
add tooltip on new unit ",https://prnt.sc/cGHNR3PiMrbX,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,VERIFIED,
setting >> complex setting >> complex unit ,after creating a bluk unit when user edit the unit it does not show the floor filed due to which user is unable to save the form,,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,VERIFIED,
setting >> complex setting >> complex unit  >> new /edit unit  // view rule ,"Category Name Not Populating or Selectable in Edit Mode:- When editing a unit: The Category Name does not appear in the Category Name field. Also, the user is unable to select a category from the dropdown list. As a result, the user cannot update the unit.
Previously saved category should be auto-filled on edit.Dropdown should allow category selection. 

Missing Auto-Invoicing Rule in Delayed Payment Charges:- In the ""Delayed Payment Charges"" section: The Auto-Invoicing Rule value is missing from the dropdown. If no specific delayed payment charges rule is created, then the Auto-Invoicing Rule should be selected by default - DONE

Missing Validation When Selecting Duplicate Unit in “Identified As” Field:- When the user selects the same unit in the “Identified As” field that was already selected earlier: The system does not show a validation error.Expected behavior:A validation message like “Unit is already selected” should appear. - DONE

Info Icon and Message Missing:- The info icon and its associated tooltip/message are missing from the relevant section. - DONE

on edit Effective date value must be present date or future date ( not past date ) this validation is missing - DONE
",,,FUNCTIONAL,OPEN,Bilal Momin Nishant Chorge,Partially Done,,,,
setting >>income setup >> auto invoicining rule ,"“Show All Rule” Link Styling:- The “Show All Rule” link is currently displayed in grey. This makes it look like plain text rather than a clickable link. The link should be in blue to clearly indicate it’s clickable. Refer to the first screenshot for visual reference.

Error on Saving Updated Rule:- When trying to save an updated rule, an error appears: Message: ""Network Error"" Refer to the second screenshot for the exact error.- DONE

UI Cleanup: Duplicate Icons and Button Rename . There is a duplicate “Remove” icon that should be eliminated. Also, the ""Edit Rule"" button should be renamed to ""Change Rule"" for better clarity. Refer to the third screenshot for confirmation. - DONE

","https://prnt.sc/VUq01OKunYsa

https://prnt.sc/1G2TXYe2moC-

https://prnt.sc/H9XD0uJcspSI",,FUNCTIONAL,OPEN,Kumail Rizvi Bilal Momin,Ready To Test,https://docs.google.com/document/d/1ZF_VssLgl_AcRPi9Qa3yfEekjBdMXMsnQ21bFblj45s/edit?usp=sharing,,,
setting >>income setup >> auto invoicining rule >> new rule >> income account >> select bank ledgers details ," Bank Account / cash Ledger – Missing Bank Name  :- In the Bank Account Ledger Details field: No bank names are being displayed, even if they exist.The dropdown should reflect available bank names for selection.

Missing Validation for Bank Account Ledger/ cash leadger :- When creating or saving an Income Account: The system allows saving even if the ""Bank Account Ledger"" field is not selected or "" cash ledger "". This is incorrect behavior.If the field is left empty, show a validation error message:“Please select Bank ledger” "" please select cash ledger ""

show all rule redirection is incorrect entry is not poper 
during edit rule the per sq ft per month filed is not auto populated ",,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,,
setting >>income setup >> late payement  rule >> edit rule / new  late charge rule ,"add late payment rule :- Info Icon and Message Missing:- The info icon and its associated tooltip/message are missing from the relevant section. ( on edit it works fine)

Incorrect Validation Error on Interest Type Selection:- When creating a new Late Charges Rule: Even after selecting the Interest Type, the system still shows an error:“Type is required” This prevents the user from saving the rule, even though the field is correctly filled. - DONE
",,,FUNCTIONAL,OPEN,Suraj Jamdade Bilal Momin,Ready To Test,,,,
setting >>income setup >> Incidental Invoicing Rules >> new rule >>add ledger ,"Effective Date Column – Future Date Not Selectable :- in the “Effective Date” column: The user is unable to select a future date, which is incorrect.The user should be allowed to select:Future dates , Current date, Past dates should not be selectable. 

New Ledger – Opening Balance Field Not Required In the New Ledger form - DONE

Nature of Group – Incorrect Field Type and Behavior. The “Nature of Group” field is currently displayed as radio buttons, which is incorrect. It should be displayed as ""Credit"" only. The field should be in a non-editable (read-only) state. for more information please check the screenshot - DONE

button not required - save & add new and reset button is not required on add ledger popup - DONE

new rule >>Info Icon and Message Missing:- The info icon and its associated tooltip/message are missing from the relevant section. - DONE

add ledger :- tool tips are missing 

after adding ledger user should redirect to new rule popup : currently its redirecting incorrectly on account module ",https://prnt.sc/z2yADfzq2G6N,,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,,
setting >>income setup ,Amenities rules section is not required ,,,FUNCTIONAL,OPEN,Bilal Momin,,,,,
setting >>income setup >> general settings,"Auto-Selected Data Not Displayed in General Settings:- In the General Settings page: Previously auto-selected/default values are not being displayed across all sections.
Expected behavior: All default or pre-saved values should be auto-populated and visible to the user upon page load. DONE

Missing “Receipt Mode” Field:- The “Receipt Mode” option is completely missing. Because of this, the user is unable to save the General Settings. Refer to the old and new society screenshots to compare and correct this issue. DONE

Missing Info Icon and Tooltip Message:-The info icon  and its help message are not visible.

general setting link",https://prnt.sc/qCpWz4k7weji,https://prnt.sc/rjmOa_LMyHO7,FUNCTIONAL,OPEN,Bilal Momin Nishant Chorge Suraj Jamdade,Ready To Test,,,,
setting >>income setup >> income account >> member income account ,"Created By” Field Missing:- The “Created By” field is not displayed in the relevant section. - DONE

member :- when user try to edit ledger named parking and it gets saved successfully but reflected the parking in invoice print name - need to discuss

Member Income Account – Ledger Selection Not Working ;- In the Member Income Account section:
Ledgers are visible/selectable from the dropdown list. but not reflecteing leadgers field . Because of this, the entire functionality cannot be completed. User should be able to select ledgers from the dropdown to proceed. - DONE",,,FUNCTIONAL,OPEN,Suraj Jamdade,Ready To Test,,,,
setting >>income setup >> income account >> non - member income account ," Unable to Add Row:- The ""Add Row"" action is not working. As a result, the user is unable to add data or test the functionality.
Clicking “Add Row” should insert a new editable row into the table. - DONE",,,FUNCTIONAL,OPEN,Nishant Chorge,Ready To Test,,,,
"setting >>income setup >>income account >>Particular Settings
","Drag and Drop Functionality Not Working:- The drag-and-drop feature is not functional. Users are unable to rearrange items or elements as expected.Drag-and-drop should allow repositioning or reordering of items as per the UI flow. - DONE

Cannot Edit Display Name:- the “Display Name” field is not editable.Users are unable to make any changes to it. - DONE

Reset Changes in Display Name Field Not Working:- After making changes in the Display Name field:The Reset functionality does not revert it to the original value. - DONE

Incorrect Help Note Content:- the help note content in the new society is incorrect. Please refer to the attached screenshot showing a comparison between the old society and new society versions.
and Update the help note content in the new society to match the correct version from the old society. - DONE
",https://prnt.sc/HwgU1Xcs1F_H,https://prnt.sc/N_rYY8eIhw-R,FUNCTIONAL,OPEN,Nishant Chorge,Ready To Test,,,,
"setting >>income setup >>income account >>Payment Reminder
","Unable to Add New Row in Payment Reminder Page:- On the Payment Reminder page: The system does not allow adding a new row. Because of this, the user is unable to test or use the complete functionality. - DONE",,,FUNCTIONAL,OPEN,Nishant Chorge,Ready To Test,,,,
setting >>expense setup >>expense accounts ,"Unable to Add New Row in expense account  Page:- On the expense page: The system does not allow adding a new row. Because of this, the user is unable to test or use the complete functionality. - DONE

expense Account – Ledger Selection Not Working ;- In the expense  Account section:
Ledgers are visible/selectable from the dropdown list. but not reflecteing leadgers field . Because of this, the entire functionality cannot be completed. User should be able to select ledgers from the dropdown to proceed. - DONE",,,FUNCTIONAL,OPEN,Nishant Chorge,Ready To Test,,,,
setting >>expense setup >>expense approval,"Unable to Add New Row in expense approval Page:- On the expense approval  page: The system does not allow adding a new row. Because of this, the user is unable to test or use the complete functionality. - DONE",,,FUNCTIONAL,OPEN,Nishant Chorge,Ready To Test,,,,
"setting >>expense setup >>Expense GST Rates
","Unable to Add New Row in Expense GST Rates Page:- On the Expense GST Rates page: The system does not allow adding a new row. Because of this, the user is unable to test or use the complete functionality. - DONE",,,FUNCTIONAL,OPEN,Nishant Chorge,Ready To Test,,,,
"setting >>expense setup >>Expense Accounts Budgets Form
","Filter Issue with ""Financial Year"" and ""Month""*:- When applying filters for ""Financial Year""* and ""Month"": No data is displayed. Because of this, the functionality cannot be tested. - DONE",,,FUNCTIONAL,OPEN,Kumail Rizvi Nishant Chorge,Ready To Test,,,,
setting >>general  >>Auto Response Email,"View Email Template Details:- Please check the screenshots of the old and new society. Compare them to identify the issue in the email template details.

Edit Email Template: -The ""Body"" label is missing a red mandatory (*).",https://prnt.sc/fnxXEebuv6VC,https://prnt.sc/9MDUBDRa7Wfm,FUNCTIONAL,OPEN,Bilal Momin,Ready To Test,,,,
setting >>general  >>Send App Notification,,,,FUNCTIONAL,OPEN,BILAL,Ready To Test,,,,
app prefrence ,not working ,,,,,,,,,,
APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

LOG_DB_URL=mongodb://
LOGDB_HOST=127.0.0.1
LOGDB_PORT=27017
LOGDB_DATABASE=
LOGDB_USERNAME=
LOGDB_M_PASSWORD=

GATEWAY_URL=
SSO_WHOMI_URL="http://127.0.0.1:8002/api/whoami"
SSO_URL="http://127.0.0.1:8002"
SSO_SERVER_API_KEY=
SSO_APP_ID=
MICRO_SERVICE_URL="http://127.0.0.1:8001/api"
LIVE_SOCIETY_URL="http://127.0.0.1:3000/"
MEETING_URL="http://stgmeet.cubeonebiz.com/"

# KEYCLOAK_BASE_URL="http://localhost:8080/"
# KEYCLOAK_CLIENT_ID=
# KEYCLOAK_CLIENT_SECRET=
# KEYCLOAK_REALM_PUBLIC_KEY="MIIBIj..."
KEYCLOAK_REALM="FSTech"
KEYCLOAK_REALM_GROUP_PREFIX="C_"
JWE_SYMMETRIC_KEY=

DB2_CONNECTION=old_sso
DB2_HOST=
DB2_PORT=3306
DB2_DATABASE=
DB2_USERNAME=
DB2_PASSWORD=

SAAS_MICROSERVICE_URL="http://127.0.0.1:8000"

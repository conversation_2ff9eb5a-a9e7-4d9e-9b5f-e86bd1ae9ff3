<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;
use Illuminate\Support\Facades\Config;

class ListTransactionDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */


    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }
    protected $signature = 'datasource:listTransactionDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the voucher reciept';
    use MongoTraits; // Use the MongoTraits trait in this class



    protected $formatterByKeys = [
        'voucher_id',
    ];

    protected $mapper = [];

    /**
     * Execute the console command.
     */

    public function apply()
    {
        // actually id is txn_id and ledger_id is ledger_id
        $txn_id = $this->input['id'];

        // $transactions = $this->tenantDB()->table('chsone_ledger_transactions as lTxn')
        // ->selectRaw('
        //     CASE WHEN lTxn.ledger_account_id = ? THEN mTxn.ledger_account_name ELSE lTxn.ledger_account_name END AS counter_ledger_account_name,
        //     CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.txn_id ELSE mTxn.txn_id END AS txn_id,
        //     CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.txn_from_id ELSE mTxn.txn_from_id END AS txn_from_id,
        //     CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.ledger_account_id ELSE mTxn.ledger_account_id END AS ledger_account_name,
        //     CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.transaction_type ELSE mTxn.transaction_type END AS transaction_type,
        //     CASE WHEN lTxn.ledger_account_id = ? THEN mTxn.ledger_account_id ELSE lTxn.ledger_account_id END AS counter_ledger_account_id,
        //     lTxn.transaction_amount,
        //     lTxn.memo_desc,
        //     lTxn.transaction_date,
        //     lTxn.soc_id,
        //     lTxn.voucher_reference_id,
        //     lTxn.voucher_reference_number,
        //     lTxn.voucher_type,
        //     lTxn.is_cancelled,
        //     lTxn.is_opening_balance,
        //     lTxn.is_reconciled,
        //     lTxn.payment_mode,
        //     lTxn.payment_reference,
        //     lTxn.other_reference_id,
        //     lTxn.created_by,
        //     lTxn.added_on,
        //     lTxn.value_date
        // ', [$ledgerId, $ledgerId, $ledgerId, $ledgerId, $ledgerId, $ledgerId])
        // ->join('chsone_ledger_transactions as mTxn', 'lTxn.txn_id', '=', 'mTxn.txn_from_id')
        // ->where(function ($query) use ($ledgerId) {
        //     $query->where('lTxn.ledger_account_id', $ledgerId)
        //         ->orWhere('mTxn.ledger_account_id', $ledgerId);
        // })
        // ->where('lTxn.txn_id', '=', $txn_id) // Condition for specific txn_id
        // ->where('lTxn.is_opening_balance', 0)
        // ->where('lTxn.transaction_amount', '!=', 0)
        // ->first();

        // fetch the details from chsone_ledger_transactions table where txn_id = $txn_id
        $to_transactions = $this->tenantDB()->table('chsone_ledger_transactions')
       ->selectRaw('
            txn_id as to_txn_id,
            txn_from_id as to_txn_from_id,
            transaction_date,
            ledger_account_id as to_ledger_id,
            ledger_account_name as to_ledger_account_name,
            transaction_amount as to_transaction_amount,
            transaction_type as to_transaction_type,
            memo_desc as to_memo_desc')
        ->where('txn_from_id', $txn_id)
        ->where('is_opening_balance', 0)
        ->where('transaction_amount', '!=', 0)
        ->first();

        $txn_from_id = $to_transactions->to_txn_from_id;

        // now fetch the details from chsone_ledger_transactions table where txn_from_id = $txn_id
        $from_transactions = $this->tenantDB()->table('chsone_ledger_transactions')
        ->selectRaw('
            txn_id as from_txn_id,
            txn_from_id,
            transaction_date,
            ledger_account_id as from_ledger_id,
            ledger_account_name as from_ledger_account_name,
            transaction_amount as from_transaction_amount,
            transaction_type as from_transaction_type,
            memo_desc as from_memo_desc')
        ->where('txn_id', $txn_from_id)
        ->where('is_opening_balance', 0)
        ->where('transaction_amount', '!=', 0)
        ->first();

        $transactions = array_merge((array) $from_transactions, (array) $to_transactions);

        if (!$transactions) {
            $this->message = 'No transactions found';
            $this->status = "error";
            $this->statusCode = 400;
            return;
        }

        $this->data = $transactions;
    }
}

<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\Config;
use Exception;
use Illuminate\Support\Facades\DB;


class ViewGroupsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:ViewGroups {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Ledger Group Tree Data Source';
    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }
    /**
     * Execute the console command.
     */

    // $query = "WITH RECURSIVE LedgerTree AS (
    //     SELECT ledger_account_id, ledger_account_name, nature_of_account, entity_type, behaviour, parent_id, 0 AS level, status, context
    //     FROM chsone_grp_ledger_tree
    //     WHERE parent_id = 0

    //     UNION ALL

    //     SELECT child.ledger_account_id, child.ledger_account_name, child.nature_of_account, child.entity_type, child.behaviour, child.parent_id, parent.level + 1, child.status, child.context
    //     FROM chsone_grp_ledger_tree AS child
    //     INNER JOIN LedgerTree AS parent ON child.parent_id = parent.ledger_account_id
    // )
    // SELECT *
    // FROM LedgerTree
    // ORDER BY level, ledger_account_id;";

    // $result = $this->tenantDB()->select($query);

    // // build the tree stucture
    // $tree = $this->buildTree($result);
    public function apply()
    {
        try {
            $soc_id = $this->input['company_id'];

            // Fetch root-level ledger accounts
            $rootAccounts = $this->tenantDB()
                ->table('chsone_grp_ledger_tree')
                // commenting this line since the requirement is to show all groups.
                ->whereIn('nature_of_account', ['dr', 'cr', 'Credit', 'Debit'])
                ->where('soc_id', $soc_id)
                ->where(function ($query) {
                    $query->where('parent_id', 0)
                        ->orWhereNull('parent_id');
                })
                ->get();

            $outerTree = [];

            // Iterate over each root-level account
            foreach ($rootAccounts as $root) {
                $parentId = $root->ledger_account_id;

                // Prepare the condition based on entity_type
                $condition = isset($_REQUEST['entity_type']) && $_REQUEST['entity_type'] == 'group'
                    ? "entity_type = 'group'"
                    :   "entity_type = 'group' AND parent_id = $parentId";

                    // dd($condition);
                // Recursive query to get children
                $query = "WITH RECURSIVE LedgerTree AS (
                    SELECT ledger_account_id, ledger_account_name, ledger_account_name as title,
                        nature_of_account, entity_type, behaviour, parent_id, 0 AS level, status, context, IFNULL(operating_type, '') as operating_type
                    FROM chsone_grp_ledger_tree
                    WHERE $condition
                    UNION ALL
                    SELECT child.ledger_account_id, child.ledger_account_name, child.ledger_account_name as title,
                        child.nature_of_account, child.entity_type, child.behaviour, child.parent_id,
                        parent.level + 1, child.status, child.context, IFNULL(child.operating_type, '') as operating_type
                    FROM chsone_grp_ledger_tree AS child
                    INNER JOIN LedgerTree AS parent ON child.parent_id = parent.ledger_account_id
                )

                SELECT * FROM LedgerTree ORDER BY level, ledger_account_name;";

                $query = $this->filter($query);
                $childrenResult = $this->tenantDB()->select($query);

                // Handle cases where no children are found
                if (empty($childrenResult)) {
                    $directResult = $this->tenantDB()
                        ->table('chsone_grp_ledger_tree')
                        ->where('soc_id', $soc_id)
                        ->where('ledger_account_id', $parentId)
                        // ->where('entity_type','group')
                        ->select(
                            'ledger_account_id',
                            'ledger_account_name',
                            'nature_of_account',
                            'entity_type',
                            'behaviour',
                            'status',
                            'context',
                            'operating_type',
                            'parent_id'
                        )
                        ->first();

                    // $directResult = json_decode(json_encode($directResult), true);
                    // if ($directResult['parent_id'] == 0) {
                    //     $outerTree[] = [
                    //         'ledger_account_id' => $directResult['ledger_account_id'],
                    //         'ledger_account_name' => $directResult['ledger_account_name'],
                    //         'nature_of_account' => $directResult['nature_of_account'],
                    //         'entity_type' => $directResult['entity_type'],
                    //         'behaviour' => $directResult['behaviour'],
                    //         'status' => $directResult['status'],
                    //         'context' => $directResult['context'],
                    //         'rows' => []
                    //     ];
                    //     continue;
                    // }
                }

                // Build the tree for the current root account
                $tree = $this->buildTree($childrenResult, $parentId);

                // Add the root account and its children to the outerTree
                $outerTree[] = [
                    'id' => $root->ledger_account_id,
                    'ledger_account_name' => ucfirst($root->ledger_account_name),
                    'nature_of_account' => $root->nature_of_account,
                    'behaviour' => ucfirst($root->behaviour),
                    'context' => ucfirst($root->context),
                    'status' => $root->status,
                    'rows' => $tree
                ];
            }

            // Add hide key recursively to the data
            $this->data = $this->addHideKeyRecursively($outerTree);
        }
        catch(Exception $e)
        {
            $this->status = 'error';
            $this->statusCode = 400;
            $this->message = $e->getMessage();
        }
    }

    // Helper function to build nested trees
    public function buildTree($data, $parentId)
    {
        try{
            $tree = [];

            foreach ($data as $row) {
                if ($row->parent_id == $parentId && $row->entity_type=="group") {
                    $node = [
                        'id' => $row->ledger_account_id,
                        'ledger_account_name' => $row->ledger_account_name,
                        'title' => $row->ledger_account_name,
                        'nature_of_account' => $row->nature_of_account,
                        'entity_type' => $row->entity_type,
                        'behaviour' => ucfirst($row->behaviour),
                        'context' => ucfirst($row->context),
                        'operating_type' => $row->operating_type,
                        'status' => $row->status,
                        'children' => $this->buildTree($data, $row->ledger_account_id)
                    ];

                    // Remove 'children' if empty
                    if (empty($node['children'])) {
                        unset($node['children']);
                    }

                    $tree[] = $node;
                }
            }

            return $tree;
        }
        catch(Exception $e)
        {
            $this->status = 'error';
            $this->statusCode = 400;
            $this->message = $e->getMessage();
        }
    }

    /**
     * Recursively add hide key to data structures
     * If data contains 'rows' or 'children' key, set hide = 1, else hide = 0
     */
    public function addHideKeyRecursively($data)
    {
        if (is_array($data)) {
            // Check if this is an empty array
            if (empty($data)) {
                return $data; // Return empty array as-is
            }

            // Check if this is an associative array (object-like) or indexed array (list-like)
            $isAssociative = array_keys($data) !== range(0, count($data) - 1);

            if ($isAssociative) {
                // This is an associative array (object-like), process it
                $result = [];

                foreach ($data as $key => $item) {
                    if (is_array($item)) {
                        // Recursively process nested arrays
                        $item = $this->addHideKeyRecursively($item);
                    }
                    $result[$key] = $item;
                }

                // Only add hide key if this is a main object (not empty rows/children arrays)
                // Check if current item has 'rows' or 'children' key and add hide key
                $hasRowsOrChildren = array_key_exists('rows', $result);

                // Only add hide key if this object has meaningful content (not just empty arrays)
                if (count($result) > 1 || !array_key_exists('rows', $result)) {
                    $result['hide'] = $hasRowsOrChildren ? 1 : 0;
                }

                return $result;
            } else {
                // This is an indexed array (list-like), just process each element
                $result = [];

                foreach ($data as $item) {
                    if (is_array($item)) {
                        $item = $this->addHideKeyRecursively($item);
                    }
                    $result[] = $item;
                }

                return $result;
            }
        }

        return $data;
    }





    // public function buildTree($data, $parentId)
    // {
    //     $tree = [];

    //     foreach ($data as $row) {
    //         if ($row->parent_id == $parentId && (!isset($_REQUEST['entity_type']) || ($_REQUEST['entity_type'] == 'group' && $row->entity_type == 'group'))) {
    //             $node = [
    //                 'id' => $row->ledger_account_id,
    //                 'ledger_account_name' => $row->ledger_account_name,
    //                 'title' => $row->ledger_account_name,
    //                 'nature_of_account' => $row->nature_of_account,
    //                 'entity_type' => $row->entity_type,
    //                 'behaviour' => $row->behaviour,
    //                 'context' => $row->context,
    //                 'status' => $row->status,
    //                 'rows' => $this->buildTree($data, $row->ledger_account_id)
    //             ];

    //             $tree[] = $node;
    //         }
    //     }

    //     return $tree;
    // }
    public function getLedgGroupTree($voucher_type, $mode, $entity_type = "", $ledger_id = '', $soc_id = 0, $behaviour = '')
    {

        $conditions = [];
        $cnd_arr = $this->contextVoucherTypes($voucher_type, $mode, $soc_id);

        // Context conditions
        if (!empty($cnd_arr['context'])) {
            $conditions[] = "context IN ('" . implode("','", $cnd_arr['context']) . "')";
        }
        if (!empty($cnd_arr['context_not'])) {
            $conditions[] = "context NOT IN ('" . implode("','", $cnd_arr['context_not']) . "')";
        }

        // Ledger account conditions
        if (!empty($ledger_id)) {
            $conditions[] = "ledger_account_id IN (" . implode(",", (array) $ledger_id) . ")";
        } elseif (!empty($cnd_arr['group'])) {
            $conditions[] = "ledger_account_id IN (" . implode(",", $cnd_arr['group']) . ")";
        }

        // Entity type conditions
        if ($entity_type == ACC_TYPE_BANK || $entity_type == ACC_TYPE_CASH) {
            $conditions[] = "(behaviour = 'asset' OR behaviour = 'liability')";
            $conditions[] = "entity_type != '" . strtolower(ENTITY_TYPE_LEDGER) . "'";
        } elseif ($entity_type == ENTITY_TYPE_GROUP) {
            $conditions[] = "entity_type != '" . strtolower(ENTITY_TYPE_LEDGER) . "'";
        }

        // Final condition
        $fin_cnd = implode(' AND ', $conditions);
        $fin_cnd .= " AND status = " . ACTIVE . " AND soc_id = $soc_id";

        // Handle voucher type 'contra'
        if ($voucher_type == 'contra') {
            $fin_cnd = "parent_id IN (" . implode(',', $cnd_arr['group']) . ") OR $fin_cnd";
        }

        if (!empty($ledger_id)) {

            return $this->getLedgerTreeByIds($ledger_id);
        }



        if (!empty($cnd_arr["group"]) && empty($cnd_arr["context"]) && empty($cnd_arr["context_not"])) {


            if (count($cnd_arr["group"]) == 1) {
                return $this->getLedgerTreeById($cnd_arr["group"][0]);
            } else {
                return $this->getLedgerTreeByIds($cnd_arr["group"]);
            }
        } else {
            return $this->getLedgerTreeByCondition($fin_cnd);
        }
    }

    public function contextVoucherTypes($voucherType, $mode, $soc_id)
    {
        $context = "";
        $contextNot = "";
        if ($mode == "from") {
            $voucherType = "payment";
            if (isset($this->constants['context_from_conf_arr'][$voucherType])) {
                $context = $this->constants['context_from_conf_arr'][$voucherType];
            }
            if (isset($this->constants['context_from_not_in_conf_arr'][$voucherType])) {
                $contextNot = $this->constants['context_from_not_in_conf_arr'][$voucherType];
            }
            if (isset($this->constants['context_from_grp_array'][$voucherType])) {
                $strings = $this->constants['context_from_grp_array'][$voucherType];
            }
        }

        if ($mode == "to") {
            if (isset($this->constants['context_to_conf_arr'][$voucherType])) {
                $context = $this->constants['context_to_conf_arr'][$voucherType];
            }
            if (isset($this->constants['context_to_not_in_conf_arr'][$voucherType])) {
                $contextNot = $this->constants['context_to_not_in_conf_arr'][$voucherType];
            }
            if (isset($this->constants['context_to_grp_array'][$voucherType])) {
                $strings = $this->constants['context_to_grp_array'][$voucherType];
            }
        }

        $groups = $this->tenantDB()->table("chsone_grp_ledger_tree")
            ->whereIn("ledger_account_name", $strings)
            ->where("soc_id", $soc_id)
            ->select("ledger_account_id")
            ->get();

        if (!empty($groups)) {
            foreach ($groups as $group) {
                $grpId[] = $group->ledger_account_id;
            }
        } else {
            $grpId = [];
        }
        return [
            "context" => $context,
            "context_not" => $contextNot,
            "group" => $grpId
        ];
    }

    public function getLedgerTreeById($id = '')
    {
        // Validate the ID to prevent SQL injection and ensure it's a valid integer
        if (empty($id) || !is_numeric($id)) {
            return [];
        }

        // Initialize the @idlist variable using your tenantDB connection
        $this->tenantDB()->statement("SET @idlist = ''");

        // Prepare the raw SQL query
        $query_string = "
        SELECT
            ledger_account_name,
            nature_of_account,
            behaviour,
            entity_type,
            ledger_account_id,
            parent_id as parent,
            status,
            context,
            operating_type
        FROM (
            SELECT *,
                CASE
                    WHEN ledger_account_id = ? THEN @idlist := CONCAT(ledger_account_id)
                    WHEN FIND_IN_SET(parent_id, @idlist) THEN @idlist := CONCAT(@idlist, ',', ledger_account_id)
                END as checkId
            FROM chsone_grp_ledger_tree
            ORDER BY ledger_account_id ASC
        ) as T
        WHERE checkId IS NOT NULL
    ";

        // Execute the raw query using your tenantDB connection and pass the ID as a parameter
        $results = $this->tenantDB()->select($query_string, [$id]);

        // Convert the results to an array and return
        return !empty($results) ? json_decode(json_encode($results), true) : [];
    }

    public function getLedgerTreeByIds($ids = [])
    {
        // Validate and sanitize input
        if (empty($ids) || !is_array($ids)) {
            return [];
        }

        // Convert the array of IDs into a comma-separated string for use in the query
        $condition = implode(',', array_map('intval', $ids));

        // Prepare the raw SQL query
        $query_string = "
        SELECT
            ledger_account_name,
            nature_of_account,
            behaviour,
            entity_type,
            ledger_account_id,
            parent_id as parent,
            status,
            context,
            operating_type
        FROM (
            SELECT *,
                CASE
                    WHEN ledger_account_id IN ($condition) THEN @idlist := CONCAT(ledger_account_id)
                    WHEN FIND_IN_SET(parent_id, @idlist) THEN @idlist := CONCAT(@idlist, ',', ledger_account_id)
                END as checkId
            FROM chsone_grp_ledger_tree, (SELECT @idlist := '') AS init
            ORDER BY parent_id, ledger_account_name, ledger_account_id ASC
        ) as T
        WHERE checkId IS NOT NULL
    ";

        // Execute the query using Laravel's DB facade
        $results = $this->tenantDB()->select(DB::raw($query_string));

        // Convert the results to an array
        return $results ? json_decode(json_encode($results), true) : [];
    }


    public function getLedgerTreeByCondition($fin_cnd)
    {
        // Validate the input condition to prevent potential SQL injection
        if (empty($fin_cnd)) {
            return [];
        }

        try {
            $this->tenantDB()->statement("SET @idlist = ''");
            // \Log::info("Executing query with condition: $fin_cnd");

            $query_string = "
                SELECT ledger_account_name, nature_of_account, behaviour, entity_type,
                ledger_account_id, parent_id as parent, status, context, operating_type
                FROM (
                    SELECT *, CASE
                        WHEN $fin_cnd THEN @idlist := CONCAT(ledger_account_id)
                        WHEN FIND_IN_SET(parent_id, @idlist) THEN @idlist := CONCAT(@idlist, ',', ledger_account_id)
                    END as checkId
                    FROM chsone_grp_ledger_tree
                    ORDER BY parent_id, ledger_account_name ASC
                ) as T
                WHERE checkId IS NOT NULL
            ";

            // \Log::info("Executing SQL Query: ", [$query_string]);

            $results = $this->tenantDB()->select($query_string);
            // \Log::info("Results: ", $results);

            return !empty($results) ? json_decode(json_encode($results), true) : [];
        } catch (\Exception $e) {
            // \Log::error("Error in getLedgerTreeByCondition: " . $e->getMessage());
            return [];
        }
    }

    public function prepareTree(array $tree, $root = 0)
    {
        $result = [];

        foreach ($tree as $key => $parent) {
            if ($parent['parent'] == $root) {
                unset($tree[$key]);

                $modifiedArray = [
                    "name" => $parent["ledger_account_name"],
                    "nature" => $parent["nature_of_account"],
                    "behaviour" => $parent["behaviour"],
                    "entity_type" => $parent["entity_type"],
                    "ledger_account_id" => $parent["ledger_account_id"],
                    "parent" => $parent["parent"],
                    "status" => $parent["status"],
                    "context" => preg_replace('/[0-9]+/', '', $parent["context"]),
                    "is_parent" => "yes",
                    "operating_type" => $parent["operating_type"]
                ];

                // Recursively find children
                $children = $this->prepareTree($tree, $parent['ledger_account_id']);
                $modifiedArray['is_parent'] = empty($children) ? 'no' : 'yes';
                $modifiedArray['children'] = $children;

                $result[$parent['ledger_account_id']] = $modifiedArray;
            }
        }

        return $result;
    }
}

<?php

namespace App\Console\Commands\Actions\DCO\AlloteeType;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class UpdateAllotteeTypeDataSource extends Action
{
    protected $signature = 'datasource:updateAllotteeType {flowId} {parentId} {input}';

    protected $description = 'Update Allottee Type';

    public function apply()
    {
        $checkId = $this->tenantDB()->table('chsone_member_type_master')
            ->where('member_type_id', $this->input['id'])
            ->first();

        if(!$checkId)
        {
            $this->status = 'error';
            $this->message = 'Member Type not found';
            $this->statusCode = 400;
            return;
        }

        $allottee_type = $this->input['allottee_type'];
        $obj = $this->tenantDB()->table('chsone_member_type_master')
            ->where('member_type_id', $this->input['id'])
            ->update([
                'member_type_name' => $allottee_type,
                'updated_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['created_by'] ?? 0
            ]);

        if ($obj)
        {
            $this->status = 'success';
            $this->message = 'Allottee Type updated successfully';
            $this->statusCode = 200;
        }
        else
        {
            $this->status = 'error';
            $this->message = 'Allottee Type not updated';
            $this->statusCode = 500;
        }


    }
}

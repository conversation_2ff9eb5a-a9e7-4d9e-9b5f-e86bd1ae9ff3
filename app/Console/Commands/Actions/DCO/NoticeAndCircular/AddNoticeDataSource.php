<?php

namespace App\Console\Commands\Actions\DCO\NoticeAndCircular;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class AddNoticeDataSource extends Action
{
    protected $signature = 'datasource:addNotice {flowId} {parentId} {input}';

    protected $description = 'Add Notice Data Source';

    public function apply()
    {
        $companyId = $this->input['company_id'];
        $type = $this->input['type'];
        $subject = $this->input['subject'];
        $body = $this->input['body'];
        $effective_from = $this->input['effective_from'];
        $published_on = $this->input['published_on'];
        $visibility = $this->input['visibility'] ?? '';

        $company = $this->masterDB()->table('chsone_societies_master')
            ->select(
                'soc_id as id',
                'soc_type_id',
                'soc_name',
                'soc_reg_num',
                'soc_address_1',
                'soc_address_2',
                'soc_landmark',
                'soc_city_or_town',
                'soc_state',
                'soc_pincode',
                'status',
                'completed',
            )
            ->where('soc_id', $companyId)
            ->where('status', 1)->first();

        $company_initials = $this->getCompanyInitials($company->soc_name);
        
        $notice_ref_no = $this->tenantDB()->table('chsone_notices')->max('notice_ref_no');
        $ref_no = str_replace($company_initials, '', $notice_ref_no);
        // $ref_no = $ref_no + 1;
        // add the company initials to the ref no and keep the number of digits to 4
        $notice_ref_no = $company_initials . str_pad($ref_no, 4, '0', STR_PAD_LEFT);
        $fk_member_id = $this->input['fk_member_id'] ?? null;

        $obj = $this->tenantDB()->table('chsone_notices')
            ->insert([
                'notice_ref_no' => $notice_ref_no,
                'soc_id' => $companyId,
                'fk_member_id' => $fk_member_id,
                'type' => $type,
                'subject' => $subject,
                'body' => $body,
                'status' => 1,
                'effective_from' => $effective_from,
                'published_on' => $published_on,
                'visibility' => $visibility,
                'created_by' => $this->input['created_by'] ?? 0,
                'created_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['updated_by'] ?? 0,
                'updated_date' => date('Y-m-d H:i:s'),
            ]);

        if($obj) {
            $this->status = 'success';
            $this->message = 'Notice added successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Failed to add notice';
            $this->statusCode = 400;
        }
        
    }
}
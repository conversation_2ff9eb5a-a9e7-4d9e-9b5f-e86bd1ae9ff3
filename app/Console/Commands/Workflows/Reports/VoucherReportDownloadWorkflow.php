<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class VoucherReportDownloadWorkflow extends Workflow
{
    protected $signature = 'workflow:voucherReportDownload {input}';

    protected $description = 'Download Voucher Report Workflow';

    protected $rules = [
    ];

    protected $rulesMessage = [
    ];

    protected $formatter = [
        'sr_no' => '',
        'voucher_type' => '',
        'transaction_date' => '',
        'from_ledger' => '',
        'to_ledger' => '',
        'amount' => ''
    ];

    protected $formatterByKeys = [
        'id'
    ];

    protected $headings = [
        'Sr.No',
        'Voucher_Type',
        'Transaction_Date',
        'From_Ledger',
        'To_Ledger',
        'Amount'
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            $voucherReportList = $this->action('datasource:ListVoucherTracker', $this->pointer, $this->request);
            // Format data to match screenshot headers
            $outputData = [];
            $srNo = 1;
            foreach ($voucherReportList as $item) {
                $outputData[] = [
                    'Sr.No' => $srNo++,
                    'voucher_type' => $item['type'] ?? '',
                    'transaction_date' => $item['transaction_date'] ?? '',
                    'from_ledger' => $item['from_ledger_account_name'] ?? '',
                    'to_ledger' => $item['to_ledger_account_name'] ?? '',
                    'Amount' => $item['amount'] ?? '',
                ];
            }
        
            $this->data = [];
            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'voucherReport_');
                $this->data['url'] = $data['data'];
            }
            else{
                
                $data = $this->hitCURLForGeneratePDF($outputData, $this->headings, 'voucherReport');
                $this->data['url'] = $data['data'];
            }
        }
    }
}

<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AllotteeTypeWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:allotteeType {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Allottee Type';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $albums = $this->action('datasource:allotteeType', $this->pointer, $this->request);
        $this->data = $albums;
        $this->meta['schema'] = $this->schema();
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "Allottee Type",
                "edit_mode" => "row",
                "select_actions" => [
                    [
                        "title" => "Update Allottee Type",
                        "icon" => "ri-edit-box-line",
                        "color" => "success",
                        "api" => [
                            'method' => 'put',
                            'url' =>  "/admin/membertype/details/:id",

                        ],
                        "rerender" => true,
                        "variant" => "contained"
                    ]
                ],
                "select_all" => true,
                "actions" => [
                    [
                        "title" => "Back",
                        "icon" => "ri-arrow-go-back-line",
                        "color" => "primary",
                        "redirect" => "/admin/member/list",
                        "variant" => "contained"
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Sr. No",
                        "key" => "id",
                    ],
                    [
                        "title" => "Allottee Type",
                        "key" => "member_type_name",
                        "editable" => "true",
                        
                    ],
                    // [
                    //     "title" => "Actions",
                    //     "type" => "actions",
                    //     "key" => "actions",
                    //     "actions" => [
                    //         [
                    //             "title" => "Action",
                    //             "icon" => "ri-play-line",
                    //             "disable_on" => [
                    //                 "defined_by" => [
                    //                     'system'
                    //                 ]
                    //             ]
                    //         ]
                    //     ]
                    // ]
                    [
                        "title" => "Actions",
                        "type" => "actions",
                        "key" => "actions",
                        "actions" => [
                            [
                                "title" => "Edit Allottee Type",
                                "icon" => "ri-edit-box-line",
                                "isedit" => true,
                                "_action" => [
                                    "add"
                                ],
                                "disable_on" => [
                                    "defined_by" => [
                                        'system'
                                    ]
                                ]
                            ],
                            [
                                "title" => "Delete",
                                "icon" => "ri-prohibited-2-line",
                                "color" => "error",
                                "api" => [
                                    'method' => 'DELETE',
                                    'url' =>  "/admin/membertype/details/:id",
    
                                ],
                                "rerender" => true,
                                "disable_on" => [
                                "defined_by" => [
                                        'system'
                                    ]
                                ]
                            ],
    
                        ]
                    ]
                ]
            ]
        ];
    }
}

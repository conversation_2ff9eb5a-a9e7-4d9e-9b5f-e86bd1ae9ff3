<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class NoticesCircularsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:NoticesCircularsWorkflow {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notice & Circular Workflow';

    // Reference No.	Subject	Type	Published on	Effective From	Action
    protected $schema = [
        'table' => [
            'tableTitle' => 'Notices & Circulars',
            'actions' => [
                [
                    "title" => "Add New",
                    "icon" => "ri-add-circle-line",
                    "redirect" => "/admin/notices/add_notice",
                ],
            ],
            'fields' => [
                '*'
            ],
            'columns' => [
                [
                    'title' => 'Reference No.',
                    'key' => 'notice_ref_no',
                    'type' => 'link',
                    'href' => '/admin/notices/view/:id',
                ],

                [
                    'title' => 'Subject',
                    'key' => 'subject'
                ],
                [
                    'title' => 'Type',
                    'key' => 'type'
                ],
                [
                    'title' => 'Published On',
                    'key' => 'published_on'
                ],
                [
                    'title' => 'Effective From',
                    'key' => 'effective_from'
                ],
                [
                    'title' => 'Actions',
                    'type' => 'actions',
                    'key' => 'actions',
                    'actions' => [
                        [
                            'title' => 'Print Notice / Circular',
                            'icon' => 'ri-printer-line',
                            'api' => [
                                'url' => '/admin/notices/print/:id',
                                'method' => 'GET',
                            ],
                        ],
                        [
                            'title' => 'View Notice / Circular',
                            'icon' => 'ri-eye-line',
                            // 'type' => 'form',
                            'href' => '/admin/notices/view/:id',
                            // 'form' => 'viewNotice',
                        ],
                        // [
                        //     'title' => 'View Responses',
                        //     'icon' => 'ri-list-check',
                        //     'form' => "noticeView",
                        //     "color" => "#0a4f92",
                        // ],
                    ]
                ]
            ]
        ]
    ];

    protected $rules = [];

    protected $rulesMessage = [];

    // protected $formatter = [
    //     'notice_id' => '',
    //     'notice_ref_no' => '',
    //     'subject' => '',
    //     'type' => '',
    //     'published_on' => '',
    //     'effective_from' => '',
    // ];

    // protected $formatterByKeys = ['notice_id'];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $this->action('datasource:NoticesCircularsDataSource', $this->pointer, $this->request);
        $this->meta['schema'] = $this->schema;
    }
}

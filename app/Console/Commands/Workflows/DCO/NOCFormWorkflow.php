<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class NOCFormWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:NOCForm {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Workflow for NOC Form';

    protected $schema = [
        'table' => [
            'tableTitle' => 'NOC',
            "actions" => [
                [
                    "title" => "New NOC",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/noc/add_noc",
                    "variant" => "contained"
                ],
            ],
            'fields' => [
                '*'
            ],
            'columns' => [
                [
                    'title' => 'Request No.',
                    'key' => 'id'
                ],
                [
                    'title' => 'Unit',
                    'key' => 'unit'
                ],
                [
                    'title' => 'Dues',
                    'key' => 'dues'
                ],
                [
                    'title' => 'Template',
                    'key' => 'purpose'
                ],
                [
                    'title' => 'Status',
                    'key' => 'status',
                    'options' => [
                        '1' => [
                            'title' => 'Unapproved'
                        ],
                        '2' => [
                            'title' => 'Approved'
                        ],
                        '3' => [
                            'title' => 'Rejected'
                        ]
                    ]
                ],
                [
                    'title' => 'Actions',
                    'key' => 'actions',
                    'type' => 'actions',
                    'actions' => [
                        [
                            'title' => 'Edit',
                            "icon" => "ri-edit-box-line",
                            'href' => '/admin/noc/add_noc/:id',
                        ],
                        [
                            'title' => 'Approve',
                            'icon' => 'ri-check-fill',
                            'href' => '/admin/noc/approveOrRejectNoc/:id/approve',
                            'disable_on' => [
                                'status' => [
                                    2,
                                ]
                            ]
                        ],
                        [
                            'title' => 'Reject',
                            'icon' => 'ri-close-line',
                            'href' => '/admin/noc/approveOrRejectNoc/:id/reject',
                            'disable_on' => [
                                'status' => [
                                    3,
                                ]
                            ]
                        ],
                        [
                            'title' => 'Download',
                            'icon' => 'ri-download-2-fill',
                            'href' => '/admin/noc/previewNocReportPrint/:id',
                        ]
                    ]
                ]
            ],
        ],
    ];

    public function apply()
    {
        $result = $this->action('datasource:NOCForm', $this->pointer, $this->request);
        $this->meta['schema'] = $this->schema;
    }
}

<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class ParkingAllotmentListWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:parkingAllotmentList {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Parking Allotment List';

    protected $hugeData = true;

    protected $schema = [
            "table" => [
                "tableTitle" => "Parking Allotments",
                "select_by" => [
                    "parking_number" => "Parking Number",
                    "unit_flat_number" => "Unit Flat Number"
                ],
                "filter_by" => [
                    "parking_type" => [
                        "title" => "Parking Type",
                        "options" => [
                            "shaded parking" => "Shaded Parking",
                            "open parking" => "Open Parking"
                        ]
                    ],
                    "allotment_for" => [
                        "title" => "Allotment For",
                        "options" => [
                            "2wheeler" => "2 Wheeler",
                            "4wheeler" => "4 Wheeler"
                        ]
                    ],
                ],
                "actions" => [
                    [
                        "title" => "New Allocate Parking",
                        "icon" => "ri-add-circle-line",
                        "color" => "primary",
                        "redirect" => "/admin/parking-allotments/add",
                        "variant" => "contained"
                    ],
                    [
                        "title" => "Print/Export",
                        "icon" => "ri-printer-line",
                        "options" => [
                            [
                                "title" => "Excel",
                                "icon" => "ri-file-excel-2-line",
                                "api" => [
                                    "url" => "/admin/parking-allotments/downloadParkingAllotments/excel",
                                    "method" => "POST",
                                    "type" => "download"
                                ]
                            ],
                            [
                                "title" => "PDF",
                                "icon" => "ri-file-pdf-2-line",
                                "api" => [
                                    "url" => "/admin/parking-allotments/downloadParkingAllotments/pdf",
                                    "method" => "POST",
                                    "type" => "download"
                                ]
                            ]
                        ]
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Parking Number",
                        // "key" => "parking_number"
                        "key" => "parking_unit_name"
                    ],
                    [
                        "title" => "Alloted To",
                        "key" => "allotment_to",
                    ],
                    [
                        "title" => "Type",
                        "key" => "allotted_parking_type"
                    ],
                    [
                        "title" => "W.E.F",
                        "key" => "effective_date"
                    ],
                    [
                        "title" => "Parking Detail",
                        "key" => "parking_detail"
                    ],
                    [
                        "title" => "Actions",
                        "type" => "actions",
                        "key" => "actions",
                        "actions" => [
                            [
                                "title" => "Edit",
                                "icon" => "ri-edit-box-line",
                                "href" => "/admin/parking-allotments/add/:id",
                                "color" => "#0a4f92",
                                "marginRight" => "5px"
                            ],
                            [
                                "title" => "Delete",
                                "icon" => "ri-delete-bin-5-line",
                                "href" => "/admin/parking-allotments/delete/:id",
                                "color" => "#0a4f92",
                                "marginRight" => "5px"
                            ]
                        ]
                    ]
                ]
            ]
        ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $parkingAllotmentList = $this->action('datasource:parkingAllotmentList', $this->pointer, $this->request);
        $this->data = $parkingAllotmentList;
        $this->meta['schema'] = $this->schema;
        $todaysDate = date('d/m/Y');
        $this->meta['schema']['table']['tableTitle'] = "Parking Allotments as on ".$todaysDate;
    }
}

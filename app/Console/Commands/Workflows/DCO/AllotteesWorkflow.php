<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use Illuminate\Support\Facades\Route;

class AllotteesWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:allottees {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Allottees List';

    protected $formatter = [
        "id" => "",
        "member_id" => "",
        "fk_unit_id" => "",
        "salute" => "",
        "member_name" => "",
        "member_email_id" => "",
        "member_mobile_number" => "",
        "effective_date" => "",
        "member_type_name" => "",
        "member_intercom" => "",
        "member_status" => "",
        "status" => "",
        "soc_building_name" => "",
        "unit_flat_number" => "",
        "building_unit" => "",
        "approved" => "",
        "user_id" => ""
    ];

    protected $formatterByKeys = ["id"];

    protected $schema =
    [
        "table" => [
            "tableTitle" => "Allottees",
            "tabs" => [
                "Approved",
                "Unapproved",
                "Primary Member Change Requests"
            ],
            "select_by" => [
                "member_name" => "Member Name",
                "member_mobile_number" => "Member Mobile Number",
                "unit_flat_number" => "Unit Number",
                "soc_building_name" => "Building",
                "building_unit" => "Building / Unit Number"
            ],
            "filter_by" => [
                "member_type_name" => [
                    "title" => "Member Type",
                    "options" => [
                        "primary" => "Primary",
                        "tenant" => "Tenant",
                        "associate" => "Associate",
                        "nominal" => "Nominal",
                    ]
                ]
            ],
            "actions" => [
                [
                    "title" => "New Allottee",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/member/register",
                    "variant" => "contained"
                ],
                [
                    "title" => "New Bulk Allottee",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/member/bulkAdd",
                    "variant" => "contained"
                ],
                [
                    "title" => "Allottee Type",
                    "icon" => "ri-list-check",
                    "color" => "primary",
                    "redirect" => "/admin/membertype/details",
                    "variant" => "contained"
                ],
                [
                    "title" => "Print/Export",
                    "icon" => "ri-printer-line",
                    "options" => [
                        [
                            "title" => "Excel",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "url" => "/admin/member/downloadMembers/excel",
                                "method" => "POST",
                                "type" => "download"
                            ]
                        ],
                        [
                            "title" => "PDF",
                            "icon" => "ri-file-pdf-2-line",
                            "api" => [
                                "url" => "/admin/member/downloadMembers/pdf",
                                "method" => "POST",
                                "type" => "download"
                            ]
                        ]
                    ]
                ]
            ],
            "fields" => [
                "*"
            ],
            "select_actions" => [
                [
                    "title" => "Activate Selected",
                    "icon" => "ri-checkbox-line",
                    "color" => "success",
                    "api" => [
                        'method' => 'put',
                        'url' =>  "/admin/member/list/1?ids={ids}",

                    ],
                    "rerender" => true,
                    "variant" => "contained"
                ],
                [
                    "title" => "Deactivate Selected",
                    "icon" => "ri-alibaba-cloud-line",
                    "color" => "error",
                    "api" => [
                        'method' => 'put',
                        'url' =>  "/admin/member/list/0?ids={ids}",

                    ],
                    "rerender" => true,
                    "variant" => "contained"
                ]
            ],
            "multi_select" => true,
            "columns" => [
                
                [
                    "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "Transfer Flat",
                            "icon" => "ri--arrow-left-right-fill",
                            "href" => "/admin/member/transferFlat/:id",
                        ]
                    ]
                ],
                
                [
                    "title" => "Building",
                    "key" => "building_unit"
                ],
                // [
                //     "title" => "Name",
                //     "key" => "member_name",
                // ],
                [
                    "title" => "Mobile No",
                    "key" => "member_mobile_number",
                ],
                [
                    "title" => "Allottee Type",
                    "key" => "member_type_name",
                ],
                [
                    "title" => "Effective From",
                    "key" => "effective_date",
                ],
                [
                    "title" => "Intercom",
                    "key" => "member_intercom"
                ],
                [
                    "title" => "Status",
                    "key" => "status",
                    "type" => "chip",
                    "options" => [
                        "0"=>[
                            "title" => "Inactive",
                            "color" => "error"
                        ],
                        "1"=>[
                            "title" => "Active",
                            "color" => "success"
                        ]
                    ]
                ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "Tenant Agreement Details",
                            "icon" => "ri-file-2-line",
                            "form" => "tenantAgreementDetails",
                            "show_on" => [
                                "member_type_name" => [
                                    "Tenant"
                                ]
                            ]
                        ],
                        [
                            "title" => "View Details",
                            "icon" => "ri-eye-line",
                            "href" => "/admin/member/viewDetails/:member_id",
                            // "api" => [
                            //     "url" => "/admin/member/viewDetails/:member_id",
                            //     "method" => "GET"
                            // ],
                            "hide_on" => [
                                "disable" => [
                                    1
                                ]
                            ]
                        ],
                        [
                            "title" => "Edit",
                            "icon" => "ri-edit-box-line",
                            // "api" => [
                            //     "url" => "/admin/member/register/:member_id",
                            //     "method" => "POST"
                            // ],
                            "href" => "/admin/member/register/:member_id",
                            // "/admin/member/register/:member_id"
                            "hide_on" => [
                                "disable" => [
                                    1
                                ]
                            ]
                        ],
                        [
                            "title" => "Deactivate",
                            "icon" => "ri-close-line",
                            // "api" => [
                            //     "url" => "/admin/member/delete/:member_id",
                            //     "method" => "DELETE"
                            // ],
                            "href" => "/admin/member/delete/:member_id",
                            "hide_on" => [
                                "disable" => [
                                    1
                                ]
                            ]
                        ],
                        // [
                        //     "title" => "Unlink User",
                        //     "icon" => "ri-link-unlink",
                        //     "hide_on" => [
                        //         "disable" => [
                        //             1
                        //         ]
                        //     ]
                        // ],
                        [
                            "title" => "Send Invite",
                            "icon" => "ri-send-plane-line",
                            "api" => [
                                "url" => "/admin/member/unitTransfer/:member_id",
                                "method" => "POST",
                                "type" => "view"
                            ],
                            "hide_on" => [
                                "disable" => [
                                    1
                                ]
                            ]
                        ],
                        [
                            "title" => "Transfer Flat",
                            "icon" => "ri-arrow-left-right-fill",
                            "api" => [
                                "url" => "/admin/member/unitTransfer/:member_id",
                                "method" => "POST",
                                "type" => "view"
                            ],
                            "hide_on" => [
                                "disable" => [
                                    1
                                ]
                            ]
                        ],
                        [
                            "title" => "Unlink ",
                            "icon" => "ri-link-unlink",
                            "api" => [
                                "url" => "/admin/member/unlinkUser/:member_id",
                                "method" => "put"
                            ],
                            "hide_on" => [
                                "user_id" => [
                                    null
                                ],
                                "disable" => [
                                    1
                                ]
                            ]
                        ],
                        [
                            "title" => "Share Certificate",
                            "type" => "link",
                            "api" => [
                                "url" => "/admin/member/addMemberShares/:id",
                                "method" => "POST"
                            ],
                            // "href" => "/admin/member/addMemberShares/:member_id",
                            "show_on" => [
                                "disable" => [
                                    1
                                ]
                            ],
                        ],
                        
                    ]
                ]
            ],
        ]
    ];

    protected $schema2 =
    [
        "table" => [
            "tableTitle" => "Allottees",
            "select_by" => [
                "member_name" => "Member Name",
                "member_mobile_number" => "Member Mobile Number",
                "unit_flat_number" => "Unit Number",
                "soc_building_name" => "Building",
                "building_unit" => "Building / Unit Number"
            ],
            "filter_by" => [
                "member_type_name" => [
                    "title" => "Member Type",
                    "options" => [
                        "primary" => "Primary",
                        "tenant" => "Tenant",
                        "associate" => "Associate",
                        "nominal" => "Nominal",
                    ]
                ]
            ],
            "actions" => [
                [
                    "title" => "New Allottee",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/member/register",
                    "variant" => "contained"
                ],
                [
                    "title" => "New Bulk Allottee",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/member/bulkAdd",
                    "variant" => "contained"
                ],
                [
                    "title" => "Allottee Type",
                    "icon" => "ri-list-check",
                    "color" => "primary",
                    "redirect" => "/admin/membertype/details",
                    "variant" => "contained"
                ],
                [
                    "title" => "Print/Export",
                    "icon" => "ri-printer-line",
                    "options" => [
                        [
                            "title" => "Excel",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "url" => "/admin/member/downloadMembers/excel",
                                "method" => "POST",
                                "type" => "download"
                            ]
                        ],
                        [
                            "title" => "PDF",
                            "icon" => "ri-file-pdf-2-line",
                            "api" => [
                                "url" => "/admin/member/downloadMembers/pdf",
                                "method" => "POST",
                                "type" => "download"
                            ]
                        ]
                    ]
                ]
            ],
            "fields" => [
                "*"
            ],
            "select_actions" => [
                [
                    "title" => "Activate Selected",
                    "icon" => "ri-checkbox-line",
                    "color" => "success",
                    "api" => [
                        'method' => 'put',
                        'url' =>  "/admin/member/list/1?ids={ids}",

                    ],
                    "rerender" => true,
                    "variant" => "contained"
                ],
                [
                    "title" => "Deactivate Selected",
                    "icon" => "ri-alibaba-cloud-line",
                    "color" => "error",
                    "api" => [
                        'method' => 'put',
                        'url' =>  "/admin/member/list/0?ids={ids}",

                    ],
                    "rerender" => true,
                    "variant" => "contained"
                ]
            ],
            "multi_select" => true,
            "columns" => [
                [
                    "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "Transfer Flat",
                            "icon" => "ri--arrow-left-right-fill",
                            "href" => "/admin/member/transferFlat/:id",
                        ]
                    ]
                ],
                
                [
                    "title" => "Building",
                    "key" => "building_unit"
                ],
                // [
                //     "title" => "Name",
                //     "key" => "member_name",
                // ],
                [
                    "title" => "Mobile No",
                    "key" => "member_mobile_number",
                ],
                [
                    "title" => "Allottee Type",
                    "key" => "member_type_name",
                ],
                [
                    "title" => "Effective From",
                    "key" => "effective_date",
                ],
                [
                    "title" => "Intercom",
                    "key" => "member_intercom"
                ],
                [
                    "title" => "Status",
                    "key" => "status",
                    "type" => "chip",
                    "options" => [
                        "0"=>[
                            "title" => "Inactive",
                            "color" => "error"
                        ],
                        "1"=>[
                            "title" => "Active",
                            "color" => "success"
                        ]
                    ]
                ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        // [
                        //     "title" => "Tenant Agreement Details",
                        //     "icon" => "ri-file-2-line",
                        //     "hide_on" => [
                        //         "disable" => [
                        //             1
                        //         ]
                        //     ]
                        // ],
                        [
                            "title" => "View Details",
                            "icon" => "ri-eye-line",
                            "href" => "/admin/member/viewDetails/:member_id",
                            // "api" => [
                            //     "url" => "/admin/member/viewDetails/:member_id",
                            //     "method" => "GET"
                            // ],
                            "hide_on" => [
                                "disable" => [
                                    1
                                ]
                            ]
                        ],
                        [
                            "title" => "Edit",
                            "icon" => "ri-edit-box-line",
                            // "api" => [
                            //     "url" => "/admin/member/register/:member_id",
                            //     "method" => "POST"
                            // ],
                            "href" => "/admin/member/register/:member_id",
                            // "/admin/member/register/:member_id"
                            "hide_on" => [
                                "disable" => [
                                    1
                                ]
                            ]
                        ],
                        [
                            "title" => "Deactivate",
                            "icon" => "ri-close-line",
                            // "api" => [
                            //     "url" => "/admin/member/delete/:member_id",
                            //     "method" => "DELETE"
                            // ],
                            "href" => "/admin/member/delete/:member_id",
                            "hide_on" => [
                                "disable" => [
                                    1
                                ]
                            ]
                        ],
                        // [
                        //     "title" => "Unlink User",
                        //     "icon" => "ri-link-unlink",
                        //     "hide_on" => [
                        //         "disable" => [
                        //             1
                        //         ]
                        //     ]
                        // ],
                        [
                            "title" => "Send Invite",
                            "icon" => "ri-send-plane-line",
                            "api" => [
                                "url" => "/admin/member/unitTransfer/:member_id",
                                "method" => "POST",
                                "type" => "view"
                            ],
                            "hide_on" => [
                                "disable" => [
                                    1
                                ]
                            ]
                        ],
                        [
                            "title" => "Transfer Flat",
                            "icon" => "ri-arrow-left-right-fill",
                            "api" => [
                                "url" => "/admin/member/unitTransfer/:member_id",
                                "method" => "POST",
                                "type" => "view"
                            ],
                            "hide_on" => [
                                "disable" => [
                                    1
                                ]
                            ]
                        ],
                        [
                            "title" => "Unlink ",
                            "icon" => "ri-link-unlink",
                            "api" => [
                                "url" => "/admin/member/unlinkUser/:member_id",
                                "method" => "put"
                            ],
                            "hide_on" => [
                                "user_id" => [
                                    null
                                ]
                            ]
                        ],
                        [
                            "title" => "Share Certificate",
                            "type" => "link",
                            "api" => [
                                "url" => "/admin/member/addMemberShares/:id",
                                "method" => "POST"
                            ],
                            // "href" => "/admin/member/addMemberShares/:member_id",
                            "show_on" => [
                                "disable" => [
                                    1
                                ]
                            ],
                        ],
                        
                    ]
                ]
            ],
        ]
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $allottees = $this->action('datasource:allottees', $this->pointer, $this->request);
        // $this->data = $this->format($allottees);
        $this->data = $allottees;
        // $this->meta['schema'] = $this->schema;

        // get route to switch query
        $currentRoute = Route::current();

        // Get the route URI pattern (e.g., "member/register/{id}")
        $routeUri = $currentRoute->uri();

        if ($routeUri == 'api/v2/admin/member/list') {
            // Fetch unique 'soc_building_name' values
            // dd($this->request);
            $buildingFilter = $this->request['building_name'] ?? null;
            $searchTerm = strtolower($this->request['search'] ?? '');

            usort($allottees, function ($a, $b) {
                preg_match('/-(\d+)/', $a['building_unit'], $matchA);
                preg_match('/-(\d+)/', $b['building_unit'], $matchB);
                return ((int)($matchA[1] ?? PHP_INT_MAX)) <=> ((int)($matchB[1] ?? PHP_INT_MAX));
            });
        
            $buildingNames = collect($allottees)->pluck('soc_building_name')->unique()->values()->toArray();
            $this->meta['building_names'] = $buildingNames;
            
            function deepSearch($data, $searchTerm) {
                foreach ($data as $value) {
                    if (is_array($value)) {
                        if (deepSearch($value, $searchTerm)) return true;
                    } elseif (is_scalar($value)) {
                        if (stripos((string) $value, $searchTerm) !== false) return true;
                    }
                }
                return false;
            }
            
            if (!empty($searchTerm)) {
                $this->data = array_values(array_filter($allottees, function ($item) use ($searchTerm) {
                    return deepSearch($item, $searchTerm);
                }));
                return;
            } elseif ($buildingFilter && in_array($buildingFilter, $buildingNames)) {
                // Filter by the specified building name from the request
                $this->data = $this->filterByBuilding($allottees, $buildingFilter);
            } else {
                // If no filter is specified or it's invalid, default to the first building name
                if (!empty($buildingNames)) {
                    $defaultBuilding = $buildingNames[0];
                    $this->data = $this->filterByBuilding($allottees, $defaultBuilding);
                }
            }
        }
        // Check if the route is 'api/admin/vendorbill/add_billable/{id}'
        if ($routeUri == 'api/admin/vendorbill/add_billable/{id}') {
            $this->meta['schema'] = [];
        } else if ($routeUri == 'api/admin/member/list/gate') {
            $this->meta['schema'] = $this->schema2;
        }  else {
            $this->meta['schema'] = $this->schema;
        }

    }

    private function filterByBuilding($allottees, $buildingName)
    {
        return collect($allottees)->filter(function ($allottee) use ($buildingName) {
            return $allottee['soc_building_name'] === $buildingName;
        })->values()->toArray();
    }


    

}

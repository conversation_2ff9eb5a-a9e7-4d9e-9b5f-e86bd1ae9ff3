<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DownloadBankAccountsWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadBankAccounts {input}';

    protected $description = 'Download Bank Accounts in excel or pdf format';



    protected $formatterByKeys = ['id'];

    protected $headings = [
        'id'=>'Sr No.',
        'bank_name'=>'Cash Account Name',
        'ledger_account_name'=>'Ledger Name',
        'account_number'=>'A/c No.',
        'bank_address'=>'Bank Address',
        'branch'=>'Bank City',
    ];

    protected $rules = [
        'type' => 'required|string|in:pdf,excel',
    ];

    protected $rulesMessage = [
        'type.required' => 'Type is required',
        'type.string' => 'Type must be a string',
        'type.in' => 'Type must be either pdf or excel',
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{

            $viewBankAccountsList = $this->action('datasource:viewBankAccountsList', $this->pointer, $this->request);
            $this->data = [];

          //map the viewBankAccountsList data to the formatter bank_name,,ledger_account_name,account_number,bank_address,branch
          $count = 0;

          $newData = array_map(function ($data) use (&$count) {
              $rowData = $data['rows'] ?? [];

              return array_map(function ($datas) use (&$count) {
                  $count++;
                  return [
                      'id' => $count,
                      'bank_name' => $datas['bank_name'] ?? '',
                      'ledger_account_name' => $datas['ledger_account_name'] ?? '',
                      'account_number' => $datas['account_number'] ?? '',
                      'bank_address' => $datas['bank_address'] ?? '',
                      'branch' => $datas['branch'] ?? '',
                  ];
              }, $rowData);
          }, $viewBankAccountsList);

          // Flatten the array if necessary
          $newData = array_merge(...$newData);

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($newData, $this->headings, 'bank_accounts_');
                $this->data['url'] = $data['data'];
            }
            else{
              $data = $this->hitCURLForGeneratePDF($newData, $this->headings, 'viewBankAccount');
                $this->data['url'] = $data['data'];
            }
        }
    }
}

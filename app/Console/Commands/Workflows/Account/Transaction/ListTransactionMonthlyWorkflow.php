<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use App\Console\Commands\Workflow;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use Illuminate\Console\Command;

class ListTransactionMonthlyWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:listTransactionMonthly {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List Monthly Transaction';

    protected $formatterByKeys = ['id'];

    /**
     * Execute the console command.
     */

    protected $rules = [
        'id' => 'required|integer',

    ];

    protected $rulesMessages = [
        'id.required' => 'Account ID is required',
        'id.integer' => 'Account ID must be an integer',

    ];

    public function apply()
    {
        $month = $this->input['month'] ?? null;
        $yearSelected = $this->input['yearly'] ?? null;
        $soc_id = $this->input['company_id'];
        if ($month != null && ($month < 1 || $month > 12)) {
            $this->status = 'error';
            $this->meta['errors']['month'] = 'Month must be between 1 and 12';
            $this->statusCode = 400;
            return;
        }

        $year = $this->input['year'] ?? null;
        $currentYear = date('Y'); // Get the current year

        if ($year != null && (!is_numeric($year) || $year < 1900 || $year > ($currentYear + 1))) {
            $this->status = 'error';
            $this->meta['errors']['year'] = 'Year must be a valid four-digit year between 1900 and ' . ($currentYear + 1);
            $this->statusCode = 400;
            return;
        }

         // Validation for yearSelected
         if ($yearSelected) {
            $yearData = explode('-', $yearSelected);

            // Validate if the input has exactly two parts after splitting
            if (count($yearData) !== 2) {
                $this->message = "Invalid Year Format";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            $start_year = intval($yearData[0]);
            $end_year = intval($yearData[1]);
            $currentYear = intval(date('Y'));

            // Validate that start_year and end_year are four-digit numbers
            if (strlen($yearData[0]) !== 4 || strlen($yearData[1]) !== 4) {
                $this->message = "Year should be in YYYY format";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            // Check if start_year is exactly one year less than end_year
            if ($end_year !== $start_year + 1) {
                $this->message = "End year must be exactly one year after the start year";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            // Check if start_year is not greater than the current year
            if ($start_year > $currentYear) {
                $this->message = "Start year cannot be greater than the current year";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            // Fetch the fy_start_date from the database
            $financialYearRecord = $this->tenantDB()->table('soc_account_financial_year_master')
                ->where('soc_id', $soc_id)
                ->orderBy('fy_start_date', 'asc')
                ->first();

            if (!$financialYearRecord) {
                $this->message = "Financial year master data not found";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }

            $fy_start_date = $financialYearRecord->fy_start_date;
            $fy_start_year = intval(date('Y', strtotime($fy_start_date)));

            if ($start_year < $fy_start_year) {
                $this->message = "Start year cannot be less than the financial year start date ($fy_start_year)";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }
        }

        $listTransactionMonthly = $this->action('datasource:listTransactionMonthly', $this->pointer, $this->request);
        $this->data = $listTransactionMonthly;

        // fetch ledger name to display table title in schema  
        $ledgerId = $this->input['id'];
        $ledger_name = ChsoneGrpLedgerTree::select('ledger_account_name')->where('ledger_account_id', $ledgerId)->first();
        $ledger_name = $ledger_name->ledger_account_name;

        $this->meta['schema'] = $this->schema();
        $this->meta['schema']['table']['tableTitle'] = 'Account: '.$ledger_name;
        $this->meta['schema']['table']['actions'][0]['options'][0]['api']['url'] = str_replace(':id', $ledgerId, $this->meta['schema']['table']['actions'][0]['options'][0]['api']['url']);
        // check if month is 4 then set table->column->first column->title to 'Opening Balance' otherwise 'Brought Forward'
        if ((isset($this->input['month']) && $this->input['month'] == 4) || (isset($this->input['filters']['month']) && $this->input['filters']['month'] == 4)) {
            $this->meta['schema']['table']['columns'][0][0]['title'] = 'Opening Balance';
        } else {
            $this->meta['schema']['table']['columns'][0][0]['title'] = 'Brought Forward';
        }
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "Account: ",
                "extraFilters" => [
                    "transaction_period" => [
                        "title" => "Transaction By",
                        "type" => "transaction_period",
                    ]
                ],
                // "extraFilters" => [
                //     "fy_start_date" => [
                //         "title" => "View as yearly",
                //         "type" => "daterange",
                //         "options" => [
                //             "start_date" => [
                //                 "title" => "Start Date",
                //                 "type" => "date",
                //             ],
                //             "end_date" => [
                //                 "title" => "End Date",
                //                 "type" => "date",
                //             ],
                //         ],
                //     ],
                // ],
                "actions" => [
                    [
                        "title" => "Print/Export",
                        "icon" => "ri-printer-line",
                        "options" => [
                            [
                                "title" => "Excel",
                                "icon" => "ri-file-excel-2-line",
                                "api" => [
                                    "method" => "POST",
                                    "url" => "/admin/transaction/listTransactionMonthly/download/excel/:id",
                                ]
                            ]
                        ]
                    ]
                ],
                "fields" => [
                    "*",
                ],
                "columns" => [
                    [
                        [
                            "title" => "Brought Forward",
                            "key" => "brought_forward",
                        ],
                        [
                            "title" => "Total Debit",
                            "key" => "total_ransaction_amount_credit",
                        ],
                        [
                            "title" => "Total Credit",
                            "key" => "total_transaction_amount_debit",
                        ],
                        [
                            "title" => "Total Balance",
                            "key" => "total_balance",
                        ]
                    ],
                    [
                        [
                            "title" => "Date",
                            "key" => "transaction_date",
                        ],
                        [
                            "title" => "Ledger Account",
                            "key" => "counter_ledger_account_name",
                            "type" => "link",
                            "href" => "/admin/transaction/listTransactionMonthly/:counter_ledger_account_id",
                        ],
                        [
                            "title" => "Narration",
                            "key" => "memo_desc",
                            "width" => 500,
                        ],
                        [
                            "title" => "Debit",
                            // "key" => "transaction_amount",
                            "key" => "transaction_amount_debit",
                        ],
                        [
                            "title" => "Credit",
                            // "key" => "transaction_amount",
                            "key" => "transaction_amount_credit",
                        ],
                        [
                            "title" => "Balance",
                            "key" => "balance",
                        ],
                        [
                            "title" => "Actions",
                            "type" => "actions",
                            "key" => "actions",
                            "actions" => [
                                [
                                    "title" => "Edit",
                                    "icon" => "ri-edit-box-line",
                                    "href" => "/admin/transaction/editTransaction/:id", // /:counter_ledger_account_id/:month/:year
                                ],
                                [
                                    "title" => "Delete",
                                    "icon" => "ri-delete-bin-5-line",
                                    "api" => [
                                        "method" => "DELETE",
                                        "url" => "admin/transaction/delete/:id", // /:counter_ledger_account_id/:month/:year
                                    ]
                                ],
                            ],
                        ],
                    ]
                ],
            ],
        ];
    }
}

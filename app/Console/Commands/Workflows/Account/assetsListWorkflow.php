<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class assetsListWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:assetsList {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List all assets for an account.';

    /**
     * Get table schema.
     */
    protected $schema = [
        "table" => [
            "tableTitle" => "Assets",
            "actions" => [
                [
                    "title" => "New Assets",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/assets/addAsset",
                ],
                [
                    "title" => "Asset Categories",
                    "icon" => "ri-list-check",
                    "color" => "secondary",
                    "redirect" => "/admin/assets/settings",
                ],
                [
                    "title" => "Print/Export",
                    "icon" => "ri-printer-line",
                    "options" => [
                        [
                            "title" => "Excel",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "url" => "/admin/assets/downloadAssets/excel",
                                "method" => "POST",
                                "type" => "download"
                            ]
                        ],
                        [
                            "title" => "PDF",
                            "icon" => "ri-file-pdf-2-line",
                            "api" => [
                                "method" => "POST",
                                "url" => "/admin/assets/downloadAssets/pdf",
                                "type" => "download"
                            ]
                        ]
                    ]
                ]
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    "title" => "Name",
                    "key" => "assets_name",
                    "type" => "link",
                    "href" => "/admin/assets/assetDetails/:id"
                ],
                [
                    "title" => "Tag No",
                    "key" => "assets_tag_number"
                ],
                [
                    "title" => "Vendor Name",
                    "key" => "vendor_name"
                ],
                [
                    "title" => "Category",
                    "key" => "assets_categories_name"
                ],
                [
                    "title" => "Location",
                    "key" => "assets_location"
                ],
                [
                    "title" => "Purchase Date",
                    "key" => "assets_purchase_date"
                ],
                [
                    "title" => "Asset Cost",
                    "key" => "assets_cost"
                ],
                [
                    "title" => "Actions",
                    "type" => "actions",
                    "key" => "actions",
                    "actions" => [
                        [
                            "title" => "Edit Asset",
                            "icon" => "ri-edit-box-line",
                            "href" => "/admin/assets/editAsset/:id",
                        ],
                        [
                            "title" => "View Asset",
                            "icon" => "ri-eye-line",
                            "href" => "/admin/assets/assetDetails/:id",
                        ]
                    ]
                ]
            ]
        ]
    ];

    protected $rules = [
        'page' => 'integer',
        'per_page' => 'integer',
    ];

    protected $messages = [
        'page.integer' => 'Page must be an integer.',
        'per_page.integer' => 'Per page must be an integer.',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $assetsList = $this->action('datasource:assetsList', $this->pointer, $this->request);
        $this->data = $assetsList;
        $this->meta['schema'] = $this->schema;
    }
}

<?php

namespace App\Console\Commands\Workflows\Account;

use App\Console\Commands\workflow;

class ProfitAndLossWorkflow extends workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:profitAndLoss {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */

     

    protected $schema = [
        "table" => [
            "tableTitle" => "Profit and Loss",
            "fields" => [
                "*"
            ],
            "tableDirection" => "row",
            "extraFilters" => [
                "acc_date" => [
                    "title" => "Accounting Date",
                    "type" => "account_date"
                ]
            ],
            "actions" => [
                [
                    "title" => "Print/Export",
                    "icon" => "ri-printer-line",
                    "options" => [
                        [
                            "title" => "Excel",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "url" => "/accountsreporting/profit_and_lossT/download/excel",
                                "method" => "GET",
                                "type" => "download"
                            ]
                        ],
                        [
                            "title" => "PDF",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "url" => "/accountsreporting/profit_and_lossT/download/pdf",
                                "method" => "GET",
                                "type" => "download"
                            ]
                        ],
                    ]
                ]
            ],
            "columns" => [
                [
                    [
                        "title" => "Income",
                        // "key" => "income"
                        "key" => "ledger_account_name",
                        "footer" => "Total"
                    ],
                ],
                [
                    [
                        "title" => "Expense",
                        // "key" => "expense"
                        "key" => "ledger_account_name",
                        "footer" => "Total"
                    ],
                ]
            ],
        ]
    ];

    public function apply()
    {
        $data = $this->action('datasource:profitAndLoss', $this->pointer, $this->request);
        $this->data = $data;
        $this->meta['schema'] = $this->schema;

        $financial_year = $this->input['lstYear'] ?? date('Y').'-'.(date('Y')+1);
        $years = explode('-',$financial_year);
        $prev_financial_year = ($years[0]-1).'_'.$years[0];
        $financial_year = $years[0].'_'.$years[1];

        $this->meta['schema']['table']['columns'][0][1]['title'] = $prev_financial_year;
        $this->meta['schema']['table']['columns'][0][1]['key'] = $prev_financial_year;
        $this->meta['schema']['table']['columns'][0][1]['type'] = "number";
        $this->meta['schema']['table']['columns'][0][1]['aggregation'] = true;
        $this->meta['schema']['table']['columns'][0][2]['title'] = $financial_year;
        $this->meta['schema']['table']['columns'][0][2]['key'] = $financial_year;
        $this->meta['schema']['table']['columns'][0][2]['type'] = "number";
        $this->meta['schema']['table']['columns'][0][2]['aggregation'] = true;
        $this->meta['schema']['table']['columns'][1][1]['title'] = $prev_financial_year;
        $this->meta['schema']['table']['columns'][1][1]['key'] = $prev_financial_year;
        $this->meta['schema']['table']['columns'][1][1]['type'] = "number";
        $this->meta['schema']['table']['columns'][1][1]['aggregation'] = true;
        $this->meta['schema']['table']['columns'][1][2]['title'] = $financial_year;
        $this->meta['schema']['table']['columns'][1][2]['key'] = $financial_year;
        $this->meta['schema']['table']['columns'][1][2]['type'] = "number";
        $this->meta['schema']['table']['columns'][1][2]['aggregation'] = true;
    }
}

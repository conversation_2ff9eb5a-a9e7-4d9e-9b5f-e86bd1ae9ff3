<?php

namespace App\Console\Commands\Workflows\Income;

use App\Console\Commands\Workflow;

class DownloadMembersUnitStatementReportWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadMembersUnitStatementReport {input}';

    protected $description = 'Download Members Unit Statement Report in Excel or PDF';

    protected $rules = [];

    protected $rulesMessage = [];

    protected $headings = [
        'Date' => 'Date',
        'Type' => 'Type',
        'Reference ID' => 'Reference ID',
        'Payment Reference' => 'Payment Reference',
        'Debit' => 'Debit',
        'Credit' => 'Credit',
        'Balance' => 'Balance'
    ];



    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $membersUnitStatement = $this->action('datasource:membersUnitStatementReport', $this->pointer, $this->request);
            // The datasource returns [detailRows, summaryTotals]
            $detailRows = $membersUnitStatement[0] ?? [];
            $summaryTotals = $membersUnitStatement[1][0] ?? [];

            $this->data = [];
            if($type == 'excel')
            {
                // Format data for Excel with proper number formatting
                $outputData = [];
                foreach ($detailRows as $item) {
                    $outputData[] = [
                        'Date' => $item['date'] ?? '',
                        'Type' => $item['type'] ?? '',
                        'Reference ID' => $item['reference_id'] ?? '',
                        'Payment Reference' => $item['payment_reference'] ?? '',
                        'Debit' => $this->formatWriteOffAmount($item['debit'] ?? 0),
                        'Credit' => $this->formatWriteOffAmount($item['credit'] ?? 0),
                        'Balance' => $this->formatWriteOffAmount($item['balance'] ?? 0)
                    ];
                }

                // Add Total row if we have summary data
                if (!empty($summaryTotals)) {
                    $outputData[] = [
                        'Date' => 'Total',
                        'Type' => '',
                        'Reference ID' => '',
                        'Payment Reference' => '',
                        'Debit' => $this->formatWriteOffAmount($summaryTotals['debit_sum'] ?? 0),
                        'Credit' => $this->formatWriteOffAmount($summaryTotals['credit_sum'] ?? 0),
                        'Balance' => $this->formatWriteOffAmount($summaryTotals['total_sum'] ?? 0)
                    ];
                }

                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'members_unit_statement_');
                $this->data['url'] = $data['data'];
            }
            else{
                
                $data = $this->hitCURLForGeneratePDF($membersUnitStatement, $this->headings, 'member_unit_ledger');
                $this->data['url'] = $data['data'];
            }
        }
    }

    /**
     * Format write-off amount to ensure zero values are displayed as "0"
     */
    private function formatWriteOffAmount($writeoff_amount)
    {
        if($writeoff_amount == 0)
        {
            return '0';
        }
        return $writeoff_amount;
    }
}

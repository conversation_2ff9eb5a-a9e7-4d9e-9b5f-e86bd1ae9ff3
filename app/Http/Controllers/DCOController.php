<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DCOController extends Controller
{
    public function parkingAllotmentList(Request $request)
    {
        return $this->workflow('workflow:parkingAllotmentList', $request->all());
    }

    public function parkingUnitsList(Request $request)
    {
        return $this->workflow('workflow:parkingUnitsList', $request->all());
    }

    public function parkingList(Request $request)
    {
        return $this->workflow('workflow:parkingList', $request->all());
    }

    public function committeeList(Request $request)
    {
        return $this->workflow('workflow:committeeList', $request->all());
    }

    public function createCommittee(Request $request)
    {
        return $this->workflow('workflow:createCommittee', $request->all());
    }

    public function committeeDetails(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:committeeDetails', $request->all());
    }

    public function committeeDetailsOfficeBearer(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:committeeDetailsOfficeBearer', $request->all());
    }

    public function committeeDetailsCommitteeMembers(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:committeeDetailsCommitteeMembers', $request->all());
    }

    public function dissolveCommittee(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:dissolveCommittee', $request->all());
    }

    public function nonmembermaster(Request $request)
    {
        return $this->workflow('workflow:NonMemberMaster', $request->all());
    }

    public function nonmembermasterById(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:nonMemberMasterById', $request->all());
    }

    public function addNonMemberMaster(Request $request)
    {
        return $this->workflow('workflow:addNonMemberMaster', $request->all());
    }

    public function updateNonMemberMaster(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:updateNonMemberMaster', $request->all());
    }

    public function noc_forms(Request $request)
    {
        return $this->workflow('workflow:NOCForm', $request->all());
    }

    public function update_noc(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:updateNOC', $request->all());
    }

    public function add_noc(Request $request)
    {
        return $this->workflow('workflow:addNOC', $request->all());
    }

    public function get_noc_by_id(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:getNOCById', $request->all());
    }

    public function downloadNoc(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:downloadNoc', $request->all());
    }

    public function approveOrRejectNoc(Request $request, $id, $status)
    {
        $request = $request->merge(["id" => $id]);
        $request = $request->merge(["status" => $status]);
        return $this->workflow('workflow:approveOrRejectNoc', $request->all());
    }

    public function noticesCirculars(Request $request)
    {
        return $this->workflow('workflow:NoticesCircularsWorkflow', $request->all());
    }

    public function noticesView(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:NoticesViewWorkflow', $request->all());
    }

    public function addNotice(Request $request)
    {
        return $this->workflow('workflow:addNotice', $request->all());
    }

    public function noticesCircularsTemplates(Request $request)
    {
        return $this->workflow('workflow:NoticesCircularsTempWorkflow', $request->all());
    }

    public function noticesCircularsTemplatesByID(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:NoticesCircularsTempByID', $request->all());
    }

    public function addNoticeTemplate(Request $request)
    {
        return $this->workflow('workflow:addNoticeTemplate', $request->all());
    }

    public function updateNoticeTemplate(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:updateNoticeTemplate', $request->all());
    }

    public function deleteNoticeTemplate(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:deleteNoticeTemplate', $request->all());
    }

    public function downloadNotices(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:downloadNotices', $request->all());
    }

    public function allocateParkingById(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:allocateParkingById', $request->all());
    }

    public function allocateParking(Request $request)
    {
        // return $this->workflow('workflow:allocate-parking', $request->all());
        return $this->workflow('workflow:allocateParking', $request->all());
    }

    public function updateAllocateParking(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:updateAllocateParking', $request->all());
    }

    public function deleteAllocateParking(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:deleteAllocateParking', $request->all());
    }

    public function downloadParkingAllotments(Request $request, $type)
    {
        $request = $request->merge(["type" => $type]);
        return $this->workflow('workflow:downloadParkingAllotments', $request->all());
    }

    public function allottees(Request $request)
    {
        return $this->workflow('workflow:allottees', $request->all());
    }

    public function unitAllottees(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:allottees', $request->all());
    }

    public function allotteeType(Request $request)
    {
        return $this->workflow('workflow:allotteeType', $request->all());
    }

    public function createAllotteeType(Request $request)
    {
        return $this->workflow('workflow:createAllotteeType', $request->all());
    }

    public function updateAllotteeType(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:updateAllotteeType', $request->all());
    }

    public function deleteAllotteeType(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:deleteAllotteeType', $request->all());
    }

    public function vehicleRegisterById(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:vehicleRegisterById', $request->all());
    }

    public function downloadVehicleRegistration(Request $request, $type)
    {
        $request = $request->merge(["type" => $type]);
        return $this->workflow('workflow:downloadVehicleRegistration', $request->all());
    }

    public function vehicleRegister(Request $request)
    {
        return $this->workflow('workflow:vehicleRegister', $request->all());
    }

    public function vehicleRegisterCardById(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:vehicleRegisterCardById', $request->all());
    }

    public function sendInvitation(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:sendInvitation', $request->all());
    }

    public function vehicleRegisterMemberListById(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:vehicleRegisterMemberListById', $request->all());
    }

    public function vehicleRegisterAllotmentListById(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:vehicleRegisterAllotmentListById', $request->all());
    }

    public function updateVehicleRegisterById(Request $request, $id, $detail_id)
    {
        $request = $request->merge(["id" => $id, "detail_id" => $detail_id]);
        return $this->workflow('workflow:updateVehicleRegisterById', $request->all());
    }

    public function deleteVehicleRegisterById(Request $request, $id)
    {
        $request = $request->merge(["id" => $id]);
        return $this->workflow('workflow:deleteVehicleRegisterById', $request->all());
    }

    public function unitWiseMembersList(Request $request)
    {
        return $this->workflow('workflow:unitWiseMembersList', $request->all());
    }

    public function unlinkUser(Request $request, $member_id)
    {
        $request = $request->merge(["id" => $member_id]);
        return $this->workflow('workflow:unlinkUser', $request->all());

    }
}

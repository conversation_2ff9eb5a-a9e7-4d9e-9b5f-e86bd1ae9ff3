<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class MemberInvitationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test member invitation API endpoint structure
     */
    public function test_member_invitation_endpoint_exists(): void
    {
        // Test that the route exists and returns a response
        // Note: This will fail without proper authentication and tenant setup
        // but it verifies the route structure is correct
        
        $response = $this->postJson('/api/admin/member/sendInvitation/1', [
            'company_id' => 1,
            'user_id' => 1
        ]);

        // The response might be 401 (unauthorized) or 400 (validation error)
        // but it should not be 404 (route not found)
        $this->assertNotEquals(404, $response->getStatusCode());
    }

    /**
     * Test that the API returns proper JSON structure
     */
    public function test_member_invitation_returns_json(): void
    {
        $response = $this->postJson('/api/admin/member/sendInvitation/1', [
            'company_id' => 1,
            'user_id' => 1
        ]);

        // Should return JSON response
        $response->assertHeader('content-type', 'application/json');
    }
}
